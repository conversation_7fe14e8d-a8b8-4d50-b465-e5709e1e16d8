# Auth-Service Certificate Sync Implementation Guide

**Update (July 2025):**
- The certificate sync process now exclusively uses the wildcard `*.chcit.org` certificate for all subdomains.
- All PowerShell scripts on Windows now use robust SSH execution methods (no longer relying on `Invoke-Expression`).
- All references to per-domain certificate mapping have been removed.
- See Troubleshooting section for details on previous errors and applied fixes.

## Overview

**Certificate Sync Now Uses Wildcard Certificate**

The sync process copies the wildcard `*.chcit.org` certificate from `project-tracker.chcit.org` to all auth-service servers. There is no longer any need for domain mapping or separate certs per subdomain.

This guide provides step-by-step instructions for implementing SSL certificate synchronization for the auth-service using a script-only approach. The solution copies *.chcit.org certificates from project-tracker.chcit.org to auth-service servers every 3 months when certbot updates them.

## Implementation Strategy

- Wildcard certificate (`*.chcit.org`) is copied and applied to all relevant servers.
- PowerShell scripts use robust SSH execution for remote sync (see Troubleshooting).
- No per-domain mapping or renaming is required.

### Script-Only Approach (Recommended)
- **Primary Tool**: Enhanced bash script based on proven git-server implementation
- **Scheduling**: <PERSON>ron job for automatic execution every 6 hours
- **Logging**: Comprehensive logging with rotation
- **Monitoring**: Health checks and notification system

## Step 1: Create Auth-Service Certificate Sync Script

### Script Location and Structure
```bash
# Create script directory
sudo mkdir -p /opt/auth-service/scripts
sudo mkdir -p /opt/auth-service/logs

# Create the certificate sync script
sudo nano /opt/auth-service/scripts/sync-auth-certificates.sh
```

### Script Configuration Variables

- `DOMAIN` is always `chcit.org`, and the certificate paths point to the wildcard certs.
- All scripts reference the wildcard certificate location.
```bash
#!/bin/bash
# Auth-Service SSL Certificate Sync Script
# Copies *.chcit.org certificates from project-tracker.chcit.org

# Configuration
MAIN_SERVER="project-tracker.chcit.org"  # or IP: ***********
MAIN_SERVER_USER="btaylor-admin"
CERT_DIR="/etc/letsencrypt"
DOMAIN="chcit.org"
LOG_DIR="/opt/auth-service/logs"
SYNC_LOG="$LOG_DIR/cert-sync.log"
SSH_KEY="/home/<USER>/.ssh/id_ed25519"
BACKUP_DIR="/home/<USER>/letsencrypt_backup"

# Auth-service specific configuration
AUTH_SERVICE_NAME="auth-service"
AUTH_SERVICE_PORT="8082"
NGINX_CONFIG_DIR="/etc/nginx/sites-available"
```

## Step 2: Enhanced Script Features

- All enhancements apply to the wildcard certificate sync.
- PowerShell scripts on Windows use `Start-Process` or equivalent for SSH, not `Invoke-Expression`.
- Troubleshooting: Previous errors stemmed from using `Invoke-Expression` for SSH and from looking for per-domain certs that did not exist. This has been fixed by using robust SSH command execution and always syncing the wildcard cert.

### Key Enhancements for Auth-Service
1. **Environment Detection**: Automatically detect dev vs prod environment
2. **Service Integration**: Restart auth-service if needed
3. **Health Checks**: Verify auth-service endpoints after certificate update
4. **Notification System**: Email/webhook notifications on success/failure
5. **Log Rotation**: Automatic log management

### Environment Detection Logic
```bash
# Detect environment based on hostname
detect_environment() {
    local hostname=$(hostname)
    if [[ "$hostname" == *"dev"* ]] || [[ "$hostname" == *"development"* ]]; then
        echo "development"
    else
        echo "production"
    fi
}

ENVIRONMENT=$(detect_environment)
log "Detected environment: $ENVIRONMENT"
```

### Auth-Service Health Check
```bash
# Test auth-service endpoints after certificate update
test_auth_service() {
    local env=$1
    local subdomain="auth"
    
    if [ "$env" = "development" ]; then
        subdomain="auth-dev"
    fi
    
    local url="https://${subdomain}.chcit.org:${AUTH_SERVICE_PORT}/health"
    log "Testing auth-service endpoint: $url"
    
    if curl -sf --connect-timeout 10 "$url" > /dev/null 2>&1; then
        log "Auth-service HTTPS endpoint test successful"
        return 0
    else
        log "Warning: Auth-service HTTPS endpoint test failed"
        return 1
    fi
}
```

## Step 3: Cron Job Configuration

### Recommended Cron Schedule
```bash
# Option 1: Every 6 hours (recommended for production)
0 */6 * * * /opt/auth-service/scripts/sync-auth-certificates.sh >> /opt/auth-service/logs/cert-sync.log 2>&1

# Option 2: Daily at 3:30 AM (offset from git server at 3:00 AM)
30 3 * * * /opt/auth-service/scripts/sync-auth-certificates.sh >> /opt/auth-service/logs/cert-sync.log 2>&1

# Option 3: Weekly on Sundays at 4:00 AM (minimal frequency)
0 4 * * 0 /opt/auth-service/scripts/sync-auth-certificates.sh >> /opt/auth-service/logs/cert-sync.log 2>&1
```

### Install Cron Job
```bash
# Add to root crontab
sudo crontab -e

# Add the chosen schedule line
0 */6 * * * /opt/auth-service/scripts/sync-auth-certificates.sh >> /opt/auth-service/logs/cert-sync.log 2>&1
```

## Step 4: Log Management

### Log Rotation Configuration
```bash
# Create logrotate configuration
sudo nano /etc/logrotate.d/auth-service-certs

# Content:
/opt/auth-service/logs/cert-sync.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
    postrotate
        # Optional: restart rsyslog if needed
        systemctl reload rsyslog > /dev/null 2>&1 || true
    endscript
}
```

## Step 5: Monitoring and Notifications

### Email Notification Setup
```bash
# Install mail utilities if not present
sudo apt update
sudo apt install mailutils -y

# Add to script for failure notifications
send_notification() {
    local status=$1
    local message=$2
    local environment=$3
    
    if command -v mail >/dev/null 2>&1; then
        echo "$message" | mail -s "Auth-Service Certificate Sync $status ($environment)" <EMAIL>
    fi
    
    # Optional: webhook notification
    if [ -n "$WEBHOOK_URL" ]; then
        curl -X POST "$WEBHOOK_URL" \
             -H "Content-Type: application/json" \
             -d "{\"status\":\"$status\",\"message\":\"$message\",\"service\":\"auth-service\",\"environment\":\"$environment\"}"
    fi
}
```

### Health Check Script
```bash
# Create separate health check script
sudo nano /opt/auth-service/scripts/check-certificates.sh

#!/bin/bash
# Check certificate expiry and validity

CERT_FILE="/etc/letsencrypt/live/chcit.org/cert.pem"
LOG_FILE="/opt/auth-service/logs/cert-check.log"

if [ -f "$CERT_FILE" ]; then
    EXPIRY=$(openssl x509 -in "$CERT_FILE" -noout -enddate | cut -d= -f2)
    EXPIRY_EPOCH=$(date -d "$EXPIRY" +%s)
    CURRENT_EPOCH=$(date +%s)
    DAYS_UNTIL_EXPIRY=$(( (EXPIRY_EPOCH - CURRENT_EPOCH) / 86400 ))
    
    echo "$(date): Certificate expires in $DAYS_UNTIL_EXPIRY days" >> "$LOG_FILE"
    
    if [ $DAYS_UNTIL_EXPIRY -lt 30 ]; then
        echo "WARNING: Certificate expires in $DAYS_UNTIL_EXPIRY days" | \
        mail -s "Auth-Service Certificate Expiry Warning" <EMAIL>
    fi
else
    echo "$(date): Certificate file not found" >> "$LOG_FILE"
fi
```

## Step 6: Testing and Validation

### Manual Testing Procedure
```bash
# 1. Test script execution
sudo /opt/auth-service/scripts/sync-auth-certificates.sh

# 2. Verify certificate files
ls -la /etc/letsencrypt/live/chcit.org/

# 3. Check certificate expiry
openssl x509 -in /etc/letsencrypt/live/chcit.org/cert.pem -noout -enddate

# 4. Test HTTPS endpoint
curl -I https://auth.chcit.org:8082/health
# or for dev:
curl -I https://auth-dev.chcit.org:8082/health

# 5. Check nginx configuration
sudo nginx -t

# 6. Verify logs
tail -f /opt/auth-service/logs/cert-sync.log
```

### Validation Checklist
- [ ] Script executes without errors
- [ ] Certificate files are present in `/etc/letsencrypt/live/chcit.org/`
- [ ] Symlinks are correctly created
- [ ] Permissions are set properly (644 for certs, 640 for privkey)
- [ ] Nginx reloads successfully
- [ ] HTTPS endpoints respond correctly
- [ ] Logs are written to correct location
- [ ] Cron job is scheduled and active

## Step 7: Deployment to Multiple Servers

### Dev Server Deployment
```bash
# Deploy to auth-dev.chcit.org
scp /opt/auth-service/scripts/sync-auth-certificates.sh <EMAIL>:/tmp/
ssh <EMAIL> "sudo mv /tmp/sync-auth-certificates.sh /opt/auth-service/scripts/"
ssh <EMAIL> "sudo chmod +x /opt/auth-service/scripts/sync-auth-certificates.sh"
```

### Production Server Deployment
```bash
# Deploy to production auth server
scp /opt/auth-service/scripts/sync-auth-certificates.sh <EMAIL>:/tmp/
ssh <EMAIL> "sudo mv /tmp/sync-auth-certificates.sh /opt/auth-service/scripts/"
ssh <EMAIL> "sudo chmod +x /opt/auth-service/scripts/sync-auth-certificates.sh"
```

## Step 8: Maintenance and Monitoring

### Regular Maintenance Tasks
1. **Monthly**: Review certificate sync logs
2. **Quarterly**: Verify certificate updates occurred during certbot renewal
3. **Annually**: Review and update script configuration
4. **As Needed**: Update server hostnames or IP addresses

### Monitoring Commands
```bash
# Check last sync status
tail -20 /opt/auth-service/logs/cert-sync.log

# Check certificate expiry
openssl x509 -in /etc/letsencrypt/live/chcit.org/cert.pem -noout -enddate

# Check cron job status
sudo crontab -l | grep sync-auth-certificates

# Test manual sync
sudo /opt/auth-service/scripts/sync-auth-certificates.sh
```

## Benefits of This Approach

1. **Simplicity**: Single script solution, easy to understand and maintain
2. **Reliability**: Based on proven git-server implementation
3. **Automation**: Fully automated with cron scheduling
4. **Monitoring**: Comprehensive logging and notification system
5. **Flexibility**: Easy to modify for different environments
6. **Cost-Effective**: No additional dependencies or complex setup

This implementation provides a robust, maintainable solution for auth-service certificate synchronization without the complexity of a C++23 application.
