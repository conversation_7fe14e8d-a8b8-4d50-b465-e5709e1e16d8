{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_ownerDocument as ownerDocument, unstable_useForkRef as useForkRef, unstable_useEventCallback as useEventCallback, unstable_createChainedFunction as createChainedFunction } from '@mui/utils';\nimport extractEventHandlers from '@mui/utils/extractEventHandlers';\nimport { ModalManager, ariaHidden } from './ModalManager';\nfunction getContainer(container) {\n  return typeof container === 'function' ? container() : container;\n}\nfunction getHasTransition(children) {\n  return children ? children.props.hasOwnProperty('in') : false;\n}\n\n// A modal manager used to track and manage the state of open Modals.\n// Modals don't open on the server so this won't conflict with concurrent requests.\nconst defaultManager = new ModalManager();\n/**\n *\n * Demos:\n *\n * - [Modal](https://mui.com/base-ui/react-modal/#hook)\n *\n * API:\n *\n * - [useModal API](https://mui.com/base-ui/react-modal/hooks-api/#use-modal)\n */\nfunction useModal(parameters) {\n  const {\n    container,\n    disableEscapeKeyDown = false,\n    disableScrollLock = false,\n    // @ts-ignore internal logic - Base UI supports the manager as a prop too\n    manager = defaultManager,\n    closeAfterTransition = false,\n    onTransitionEnter,\n    onTransitionExited,\n    children,\n    onClose,\n    open,\n    rootRef\n  } = parameters;\n\n  // @ts-ignore internal logic\n  const modal = React.useRef({});\n  const mountNodeRef = React.useRef(null);\n  const modalRef = React.useRef(null);\n  const handleRef = useForkRef(modalRef, rootRef);\n  const [exited, setExited] = React.useState(!open);\n  const hasTransition = getHasTransition(children);\n  let ariaHiddenProp = true;\n  if (parameters['aria-hidden'] === 'false' || parameters['aria-hidden'] === false) {\n    ariaHiddenProp = false;\n  }\n  const getDoc = () => ownerDocument(mountNodeRef.current);\n  const getModal = () => {\n    modal.current.modalRef = modalRef.current;\n    modal.current.mount = mountNodeRef.current;\n    return modal.current;\n  };\n  const handleMounted = () => {\n    manager.mount(getModal(), {\n      disableScrollLock\n    });\n\n    // Fix a bug on Chrome where the scroll isn't initially 0.\n    if (modalRef.current) {\n      modalRef.current.scrollTop = 0;\n    }\n  };\n  const handleOpen = useEventCallback(() => {\n    const resolvedContainer = getContainer(container) || getDoc().body;\n    manager.add(getModal(), resolvedContainer);\n\n    // The element was already mounted.\n    if (modalRef.current) {\n      handleMounted();\n    }\n  });\n  const isTopModal = React.useCallback(() => manager.isTopModal(getModal()), [manager]);\n  const handlePortalRef = useEventCallback(node => {\n    mountNodeRef.current = node;\n    if (!node) {\n      return;\n    }\n    if (open && isTopModal()) {\n      handleMounted();\n    } else if (modalRef.current) {\n      ariaHidden(modalRef.current, ariaHiddenProp);\n    }\n  });\n  const handleClose = React.useCallback(() => {\n    manager.remove(getModal(), ariaHiddenProp);\n  }, [ariaHiddenProp, manager]);\n  React.useEffect(() => {\n    return () => {\n      handleClose();\n    };\n  }, [handleClose]);\n  React.useEffect(() => {\n    if (open) {\n      handleOpen();\n    } else if (!hasTransition || !closeAfterTransition) {\n      handleClose();\n    }\n  }, [open, handleClose, hasTransition, closeAfterTransition, handleOpen]);\n  const createHandleKeyDown = otherHandlers => event => {\n    var _otherHandlers$onKeyD;\n    (_otherHandlers$onKeyD = otherHandlers.onKeyDown) == null || _otherHandlers$onKeyD.call(otherHandlers, event);\n\n    // The handler doesn't take event.defaultPrevented into account:\n    //\n    // event.preventDefault() is meant to stop default behaviors like\n    // clicking a checkbox to check it, hitting a button to submit a form,\n    // and hitting left arrow to move the cursor in a text input etc.\n    // Only special HTML elements have these default behaviors.\n    if (event.key !== 'Escape' || event.which === 229 ||\n    // Wait until IME is settled.\n    !isTopModal()) {\n      return;\n    }\n    if (!disableEscapeKeyDown) {\n      // Swallow the event, in case someone is listening for the escape key on the body.\n      event.stopPropagation();\n      if (onClose) {\n        onClose(event, 'escapeKeyDown');\n      }\n    }\n  };\n  const createHandleBackdropClick = otherHandlers => event => {\n    var _otherHandlers$onClic;\n    (_otherHandlers$onClic = otherHandlers.onClick) == null || _otherHandlers$onClic.call(otherHandlers, event);\n    if (event.target !== event.currentTarget) {\n      return;\n    }\n    if (onClose) {\n      onClose(event, 'backdropClick');\n    }\n  };\n  const getRootProps = (otherHandlers = {}) => {\n    const propsEventHandlers = extractEventHandlers(parameters);\n\n    // The custom event handlers shouldn't be spread on the root element\n    delete propsEventHandlers.onTransitionEnter;\n    delete propsEventHandlers.onTransitionExited;\n    const externalEventHandlers = _extends({}, propsEventHandlers, otherHandlers);\n    return _extends({\n      role: 'presentation'\n    }, externalEventHandlers, {\n      onKeyDown: createHandleKeyDown(externalEventHandlers),\n      ref: handleRef\n    });\n  };\n  const getBackdropProps = (otherHandlers = {}) => {\n    const externalEventHandlers = otherHandlers;\n    return _extends({\n      'aria-hidden': true\n    }, externalEventHandlers, {\n      onClick: createHandleBackdropClick(externalEventHandlers),\n      open\n    });\n  };\n  const getTransitionProps = () => {\n    const handleEnter = () => {\n      setExited(false);\n      if (onTransitionEnter) {\n        onTransitionEnter();\n      }\n    };\n    const handleExited = () => {\n      setExited(true);\n      if (onTransitionExited) {\n        onTransitionExited();\n      }\n      if (closeAfterTransition) {\n        handleClose();\n      }\n    };\n    return {\n      onEnter: createChainedFunction(handleEnter, children == null ? void 0 : children.props.onEnter),\n      onExited: createChainedFunction(handleExited, children == null ? void 0 : children.props.onExited)\n    };\n  };\n  return {\n    getRootProps,\n    getBackdropProps,\n    getTransitionProps,\n    rootRef: handleRef,\n    portalRef: handlePortalRef,\n    isTopModal,\n    exited,\n    hasTransition\n  };\n}\nexport default useModal;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}