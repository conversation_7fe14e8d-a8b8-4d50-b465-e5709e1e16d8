#include "http_server.hpp"
#include "user_manager.hpp"
#include "jwt_manager.hpp"
#include "rate_limiter.hpp"
#include "rbac_manager.hpp"
#include "enhanced_token_manager.hpp"
#include <nlohmann/json.hpp>
#include <iostream>
#include <thread>
#include <chrono>
#include <sstream>
#include <regex>
#include <httplib.h>

using json = nlohmann::json;

HttpServer::HttpServer(UserManager* user_manager, JWTManager* jwt_manager,
                       RBACManager* rbac_manager, EnhancedTokenManager* enhanced_token_manager)
    : user_manager_(user_manager), jwt_manager_(jwt_manager),
      rbac_manager_(rbac_manager), enhanced_token_manager_(enhanced_token_manager),
      rate_limiter_(std::make_unique<RateLimiter>()), running_(false), server_port_(0) {

    if (!rbac_manager_) {
        throw std::invalid_argument("RBACManager cannot be null");
    }
    if (!enhanced_token_manager_) {
        throw std::invalid_argument("EnhancedTokenManager cannot be null");
    }

    initializeRoutes();
    std::cout << "HTTP Server initialized with OAuth 2.0 API endpoints, RBAC, and Enhanced Token Management" << std::endl;
}

HttpServer::~HttpServer() {
    stop();
}

void HttpServer::initializeRoutes() {
    // OAuth 2.0 API endpoints
    routes_["POST:/oauth/token"] = [this](const HttpRequest& req) { return handleTokenRequest(req); };
    routes_["POST:/oauth/refresh"] = [this](const HttpRequest& req) { return handleRefreshRequest(req); };
    routes_["POST:/oauth/validate"] = [this](const HttpRequest& req) { return handleValidateRequest(req); };
    routes_["POST:/oauth/revoke"] = [this](const HttpRequest& req) { return handleRevokeRequest(req); };
    routes_["GET:/health"] = [this](const HttpRequest& req) { return handleHealthCheck(req); };

    // Enhanced RBAC API endpoints
    routes_["GET:/api/users/{id}/roles"] = [this](const HttpRequest& req) { return handleGetUserRoles(req); };
    routes_["POST:/api/users/{id}/roles"] = [this](const HttpRequest& req) { return handleAssignUserRole(req); };
    routes_["DELETE:/api/users/{id}/roles"] = [this](const HttpRequest& req) { return handleRemoveUserRole(req); };
    routes_["GET:/api/users/{id}/permissions"] = [this](const HttpRequest& req) { return handleGetUserPermissions(req); };
    routes_["GET:/api/projects/{id}/users"] = [this](const HttpRequest& req) { return handleGetProjectUsers(req); };
    routes_["GET:/api/organizations"] = [this](const HttpRequest& req) { return handleGetOrganizations(req); };
    routes_["GET:/api/projects"] = [this](const HttpRequest& req) { return handleGetProjects(req); };

    // Admin User Management API endpoints
    routes_["GET:/api/admin/users"] = [this](const HttpRequest& req) { return handleGetAllUsers(req); };
    routes_["POST:/api/admin/users"] = [this](const HttpRequest& req) { return handleCreateUser(req); };
    routes_["PUT:/api/admin/users/{id}"] = [this](const HttpRequest& req) { return handleUpdateUser(req); };
    routes_["DELETE:/api/admin/users/{id}"] = [this](const HttpRequest& req) { return handleDeleteUser(req); };

    // Admin Role Management API endpoints
    routes_["GET:/api/admin/roles"] = [this](const HttpRequest& req) { return handleGetAllRoles(req); };
    routes_["POST:/api/admin/roles"] = [this](const HttpRequest& req) { return handleCreateRole(req); };
    routes_["PUT:/api/admin/roles/{id}"] = [this](const HttpRequest& req) { return handleUpdateRole(req); };
    routes_["DELETE:/api/admin/roles/{id}"] = [this](const HttpRequest& req) { return handleDeleteRole(req); };

    // Admin Permission Management API endpoints
    routes_["GET:/api/admin/permissions"] = [this](const HttpRequest& req) { return handleGetAllPermissions(req); };
    routes_["POST:/api/admin/permissions"] = [this](const HttpRequest& req) { return handleCreatePermission(req); };
    routes_["PUT:/api/admin/permissions/{id}"] = [this](const HttpRequest& req) { return handleUpdatePermission(req); };
    routes_["DELETE:/api/admin/permissions/{id}"] = [this](const HttpRequest& req) { return handleDeletePermission(req); };

    // Admin User Assignment API endpoints
    routes_["GET:/api/admin/user-assignments"] = [this](const HttpRequest& req) { return handleGetUserAssignments(req); };
    routes_["POST:/api/admin/user-assignments"] = [this](const HttpRequest& req) { return handleCreateUserAssignment(req); };
    routes_["DELETE:/api/admin/user-assignments/{id}"] = [this](const HttpRequest& req) { return handleDeleteUserAssignment(req); };

    std::cout << "OAuth 2.0 + Enhanced RBAC + Admin API routes initialized:" << std::endl;
    std::cout << "  POST /oauth/token - Token generation (with project scoping)" << std::endl;
    std::cout << "  POST /oauth/refresh - Token refresh" << std::endl;
    std::cout << "  POST /oauth/validate - Token validation" << std::endl;
    std::cout << "  POST /oauth/revoke - Token revocation" << std::endl;
    std::cout << "  GET /health - Health check" << std::endl;
    std::cout << "  GET /api/users/{id}/roles - Get user roles" << std::endl;
    std::cout << "  POST /api/users/{id}/roles - Assign user role" << std::endl;
    std::cout << "  DELETE /api/users/{id}/roles - Remove user role" << std::endl;
    std::cout << "  GET /api/users/{id}/permissions - Get user permissions" << std::endl;
    std::cout << "  GET /api/projects/{id}/users - Get project users" << std::endl;
    std::cout << "  GET /api/organizations - List organizations" << std::endl;
    std::cout << "  GET /api/projects - List projects" << std::endl;
    std::cout << "  GET /api/admin/users - Get all users" << std::endl;
    std::cout << "  POST /api/admin/users - Create user" << std::endl;
    std::cout << "  PUT /api/admin/users/{id} - Update user" << std::endl;
    std::cout << "  DELETE /api/admin/users/{id} - Delete user" << std::endl;
    std::cout << "  GET /api/admin/roles - Get all roles" << std::endl;
    std::cout << "  POST /api/admin/roles - Create role" << std::endl;
    std::cout << "  GET /api/admin/permissions - Get all permissions" << std::endl;
    std::cout << "  POST /api/admin/permissions - Create permission" << std::endl;
    std::cout << "  GET /api/admin/user-assignments - Get user assignments" << std::endl;
    std::cout << "  POST /api/admin/user-assignments - Create user assignment" << std::endl;
}

void HttpServer::start(int port) {
    std::cout << "Starting real HTTP server on port " << port << "..." << std::endl;
    running_ = true;
    server_port_ = port;

    // Create httplib server
    httplib::Server server;

    // Set CORS headers for all responses
    server.set_pre_routing_handler([](const httplib::Request& req, httplib::Response& res) {
        res.set_header("Access-Control-Allow-Origin", "*");
        res.set_header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        res.set_header("Access-Control-Allow-Headers", "Content-Type, Authorization");
        return httplib::Server::HandlerResponse::Unhandled;
    });

    // Handle OPTIONS requests for CORS
    server.Options(".*", [](const httplib::Request&, httplib::Response& res) {
        return;
    });

    // Health endpoint
    server.Get("/health", [this](const httplib::Request& req, httplib::Response& res) {
        HttpRequest http_req;
        http_req.method = "GET";
        http_req.path = "/health";

        auto http_res = handleHealthCheck(http_req);
        res.status = http_res.status_code;
        res.set_content(http_res.body, "application/json");
    });

    // OAuth 2.0 Token endpoint
    server.Post("/oauth/token", [this](const httplib::Request& req, httplib::Response& res) {
        HttpRequest http_req;
        http_req.method = "POST";
        http_req.path = "/oauth/token";
        http_req.body = req.body;

        // Copy headers
        for (const auto& header : req.headers) {
            http_req.headers[header.first] = header.second;
        }

        // Check rate limit
        std::string client_ip = req.get_header_value("X-Real-IP");
        if (client_ip.empty()) {
            client_ip = req.get_header_value("X-Forwarded-For");
        }
        if (client_ip.empty()) {
            client_ip = "127.0.0.1"; // fallback for local requests
        }

        auto rate_limit_response = handleRateLimit(client_ip, "/oauth/token");
        if (rate_limit_response.status_code != 200) {
            res.status = rate_limit_response.status_code;
            res.set_content(rate_limit_response.body, "application/json");
            return;
        }

        auto http_res = handleTokenRequest(http_req);
        res.status = http_res.status_code;
        res.set_content(http_res.body, "application/json");
    });

    // OAuth 2.0 Token refresh endpoint
    server.Post("/oauth/refresh", [this](const httplib::Request& req, httplib::Response& res) {
        HttpRequest http_req;
        http_req.method = "POST";
        http_req.path = "/oauth/refresh";
        http_req.body = req.body;

        for (const auto& header : req.headers) {
            http_req.headers[header.first] = header.second;
        }

        auto http_res = handleRefreshRequest(http_req);
        res.status = http_res.status_code;
        res.set_content(http_res.body, "application/json");
    });

    // OAuth 2.0 Token validation endpoint
    server.Post("/oauth/validate", [this](const httplib::Request& req, httplib::Response& res) {
        HttpRequest http_req;
        http_req.method = "POST";
        http_req.path = "/oauth/validate";
        http_req.body = req.body;

        for (const auto& header : req.headers) {
            http_req.headers[header.first] = header.second;
        }

        auto http_res = handleValidateRequest(http_req);
        res.status = http_res.status_code;
        res.set_content(http_res.body, "application/json");
    });

    // OAuth 2.0 Token revocation endpoint
    server.Post("/oauth/revoke", [this](const httplib::Request& req, httplib::Response& res) {
        HttpRequest http_req;
        http_req.method = "POST";
        http_req.path = "/oauth/revoke";
        http_req.body = req.body;

        for (const auto& header : req.headers) {
            http_req.headers[header.first] = header.second;
        }

        auto http_res = handleRevokeRequest(http_req);
        res.status = http_res.status_code;
        res.set_content(http_res.body, "application/json");
    });

    std::cout << "Real HTTP server configured with OAuth 2.0 endpoints:" << std::endl;
    std::cout << "  GET  http://localhost:" << port << "/health" << std::endl;
    std::cout << "  POST http://localhost:" << port << "/oauth/token" << std::endl;
    std::cout << "  POST http://localhost:" << port << "/oauth/refresh" << std::endl;
    std::cout << "  POST http://localhost:" << port << "/oauth/validate" << std::endl;
    std::cout << "  POST http://localhost:" << port << "/oauth/revoke" << std::endl;
    std::cout << "\nStarting HTTP server listener..." << std::endl;

    // Start the server (this will block)
    if (!server.listen("0.0.0.0", port)) {
        std::cerr << "Failed to start HTTP server on port " << port << std::endl;
        running_ = false;
    }
}

void HttpServer::stop() {
    if (running_) {
        std::cout << "Stopping HTTP server..." << std::endl;
        running_ = false;
        // Note: httplib::Server::stop() would be called here if we had a server instance
        // For now, the server will stop when the process terminates
    }
}

HttpServer::HttpResponse HttpServer::processRequest(const HttpRequest& request) {
    std::string route_key = request.method + ":" + request.path;

    auto route_it = routes_.find(route_key);
    if (route_it != routes_.end()) {
        try {
            return route_it->second(request);
        } catch (const std::exception& e) {
            return createErrorResponse(500, "internal_server_error",
                                     "Internal server error: " + std::string(e.what()));
        }
    }

    return createErrorResponse(404, "not_found", "Endpoint not found");
}

HttpServer::HttpResponse HttpServer::handleTokenRequest(const HttpRequest& request) {
    try {
        auto params = parseJsonBody(request.body);

        // Extract credentials
        std::string username = params["username"];
        std::string password = params["password"];
        std::string grant_type = (params.find("grant_type") != params.end()) ? params["grant_type"] : "password";

        // Enhanced RBAC: Extract optional project_id for project-scoped tokens
        std::string project_id = (params.find("project_id") != params.end()) ? params["project_id"] : "";
        std::string scope = (params.find("scope") != params.end()) ? params["scope"] : "read write";

        if (username.empty() || password.empty()) {
            return createErrorResponse(400, "invalid_request", "Missing username or password");
        }

        if (grant_type != "password") {
            return createErrorResponse(400, "unsupported_grant_type", "Only password grant type is supported");
        }

        // Authenticate user with database
        auto auth_result = user_manager_->authenticate_user(username, password);

        if (!auth_result.success) {
            return createErrorResponse(401, "invalid_grant", "Invalid username or password");
        }

        std::string user_id = auth_result.user_id;  // UUID

        // Enhanced RBAC: Check if project-scoped token is requested
        if (!project_id.empty()) {
            return handleProjectScopedTokenRequest(user_id, project_id, scope, request);
        }

        // Legacy OAuth 2.0 flow: Generate standard tokens
        std::vector<std::string> scopes = {"read", "write"};
        auto token_pair = jwt_manager_->generateTokenPair(user_id, scopes);

        if (!token_pair.success) {
            return createErrorResponse(500, "token_generation_failed", token_pair.error_message);
        }

        // Create OAuth 2.0 token response
        json response;
        response["access_token"] = token_pair.access_token.token;
        response["refresh_token"] = token_pair.refresh_token.token;
        response["token_type"] = "Bearer";
        response["expires_in"] = std::chrono::duration_cast<std::chrono::seconds>(
            token_pair.access_token.expires_at - std::chrono::system_clock::now()).count();
        response["scope"] = "read write";

        return createSuccessResponse(response.dump());

    } catch (const std::exception& e) {
        return createErrorResponse(400, "invalid_request", "Invalid request format: " + std::string(e.what()));
    }
}

HttpServer::HttpResponse HttpServer::handleProjectScopedTokenRequest(const std::string& user_id,
                                                                   const std::string& project_id,
                                                                   const std::string& scope,
                                                                   const HttpRequest& request) {
    try {
        // Validate project_id format (basic UUID format check)
        if (project_id.length() != 36 || project_id.find('-') == std::string::npos) {
            return createErrorResponse(400, "invalid_request", "Invalid project_id format");
        }

        // Check if user has access to the project
        auto user_roles = rbac_manager_->getUserProjectRoles(user_id, project_id);
        if (user_roles.empty()) {
            return createErrorResponse(403, "access_denied", "User has no access to the specified project");
        }

        // Extract client information
        std::string client_ip = extractClientIP(request);
        std::string user_agent = (request.headers.find("User-Agent") != request.headers.end())
                                ? request.headers.at("User-Agent") : "Unknown";

        // Parse requested scopes
        std::vector<std::string> requested_scopes;
        std::istringstream scope_stream(scope);
        std::string single_scope;
        while (scope_stream >> single_scope) {
            requested_scopes.push_back(single_scope);
        }

        // Create enhanced token request
        EnhancedTokenManager::TokenRequest token_request;
        token_request.user_id = user_id;
        token_request.project_id = project_id;
        token_request.requested_scopes = requested_scopes;
        token_request.client_ip = client_ip;
        token_request.user_agent = user_agent;
        token_request.access_token_lifetime = std::chrono::hours(1);  // Default 1 hour
        token_request.refresh_token_lifetime = std::chrono::hours(24 * 7);  // Default 7 days

        // Generate project-scoped tokens
        auto token_pair = enhanced_token_manager_->generateProjectTokens(token_request);

        if (token_pair.first.empty() || token_pair.second.empty()) {
            return createErrorResponse(500, "token_generation_failed", "Failed to generate project-scoped tokens");
        }

        // Get user permissions for the project
        auto permissions = rbac_manager_->getUserProjectPermissions(user_id, project_id);

        // Create enhanced OAuth 2.0 token response
        json response;
        response["access_token"] = token_pair.first;
        response["refresh_token"] = token_pair.second;
        response["token_type"] = "Bearer";
        response["expires_in"] = 3600;  // 1 hour in seconds
        response["scope"] = scope;
        response["project_id"] = project_id;
        response["permissions"] = permissions;

        std::cout << "Project-scoped tokens generated for user: " << user_id
                  << " in project: " << project_id << std::endl;

        return createSuccessResponse(response.dump());

    } catch (const std::exception& e) {
        return createErrorResponse(500, "server_error", "Error generating project-scoped tokens: " + std::string(e.what()));
    }
}

// ============================================================================
// RBAC Management API Endpoints
// ============================================================================

HttpServer::HttpResponse HttpServer::handleGetUserRoles(const HttpRequest& request) {
    try {
        // Extract user_id from path (simplified path parsing)
        std::string path = request.path;
        std::regex user_id_regex(R"(/api/users/([^/]+)/roles)");
        std::smatch matches;

        if (!std::regex_match(path, matches, user_id_regex)) {
            return createErrorResponse(400, "invalid_request", "Invalid path format");
        }

        std::string user_id = matches[1].str();

        // Validate user_id format (basic UUID format check)
        if (user_id.length() != 36 || user_id.find('-') == std::string::npos) {
            return createErrorResponse(400, "invalid_request", "Invalid user_id format");
        }

        // Get query parameters for project_id (optional)
        std::string project_id;
        auto query_pos = request.path.find('?');
        if (query_pos != std::string::npos) {
            std::string query = request.path.substr(query_pos + 1);
            // Simple query parsing for project_id
            if (query.find("project_id=") == 0) {
                project_id = query.substr(11);
            }
        }

        json response;
        response["user_id"] = user_id;

        if (!project_id.empty()) {
            // Get user roles for specific project
            auto roles = rbac_manager_->getUserProjectRoles(user_id, project_id);
            response["project_id"] = project_id;
            response["roles"] = json::array();

            for (const auto& role : roles) {
                json role_obj;
                role_obj["role_id"] = role.role_id;
                role_obj["role_name"] = role.role_name;
                // Note: assigned_at timestamp would need to be added to Role struct
                role_obj["assigned_at"] = std::chrono::duration_cast<std::chrono::seconds>(
                    std::chrono::system_clock::now().time_since_epoch()).count();
                response["roles"].push_back(role_obj);
            }
        } else {
            // Get all user roles across all projects
            response["roles"] = json::array();
            // This would require a new method in RBACManager to get all user roles
            // For now, return empty array with message
            response["message"] = "Use project_id parameter to get roles for specific project";
        }

        return createSuccessResponse(response.dump());

    } catch (const std::exception& e) {
        return createErrorResponse(500, "server_error", "Error retrieving user roles: " + std::string(e.what()));
    }
}

HttpServer::HttpResponse HttpServer::handleAssignUserRole(const HttpRequest& request) {
    try {
        // Extract user_id from path
        std::string path = request.path;
        std::regex user_id_regex(R"(/api/users/([^/]+)/roles)");
        std::smatch matches;

        if (!std::regex_match(path, matches, user_id_regex)) {
            return createErrorResponse(400, "invalid_request", "Invalid path format");
        }

        std::string user_id = matches[1].str();

        // Validate user_id format (basic UUID format check)
        if (user_id.length() != 36 || user_id.find('-') == std::string::npos) {
            return createErrorResponse(400, "invalid_request", "Invalid user_id format");
        }

        // Parse request body
        auto params = parseJsonBody(request.body);
        std::string project_id = params["project_id"];
        std::string role_id = params["role_id"];

        if (project_id.empty() || role_id.empty()) {
            return createErrorResponse(400, "invalid_request", "Missing project_id or role_id");
        }

        // Validate UUIDs (basic format check)
        if ((project_id.length() != 36 || project_id.find('-') == std::string::npos) ||
            (role_id.length() != 36 || role_id.find('-') == std::string::npos)) {
            return createErrorResponse(400, "invalid_request", "Invalid project_id or role_id format");
        }

        // Assign role to user
        bool result = rbac_manager_->assignUserProjectRole(user_id, project_id, role_id);

        if (!result) {
            return createErrorResponse(400, "assignment_failed", "Failed to assign role to user");
        }

        json response;
        response["success"] = true;
        response["user_id"] = user_id;
        response["project_id"] = project_id;
        response["role_id"] = role_id;
        response["message"] = "Role assigned successfully";

        std::cout << "Role " << role_id << " assigned to user " << user_id
                  << " in project " << project_id << std::endl;

        return createSuccessResponse(response.dump());

    } catch (const std::exception& e) {
        return createErrorResponse(500, "server_error", "Error assigning user role: " + std::string(e.what()));
    }
}

HttpServer::HttpResponse HttpServer::handleRemoveUserRole(const HttpRequest& request) {
    try {
        // Extract user_id from path
        std::string path = request.path;
        std::regex user_id_regex(R"(/api/users/([^/]+)/roles)");
        std::smatch matches;

        if (!std::regex_match(path, matches, user_id_regex)) {
            return createErrorResponse(400, "invalid_request", "Invalid path format");
        }

        std::string user_id = matches[1].str();

        // Parse request body
        auto params = parseJsonBody(request.body);
        std::string project_id = params["project_id"];
        std::string role_id = params["role_id"];

        if (project_id.empty() || role_id.empty()) {
            return createErrorResponse(400, "invalid_request", "Missing project_id or role_id");
        }

        // Remove role from user
        bool result = rbac_manager_->removeUserProjectRole(user_id, project_id, role_id);

        if (!result) {
            return createErrorResponse(400, "removal_failed", "Failed to remove role from user");
        }

        json response;
        response["success"] = true;
        response["message"] = "Role removed successfully";

        return createSuccessResponse(response.dump());

    } catch (const std::exception& e) {
        return createErrorResponse(500, "server_error", "Error removing user role: " + std::string(e.what()));
    }
}

HttpServer::HttpResponse HttpServer::handleGetUserPermissions(const HttpRequest& request) {
    try {
        // Extract user_id from path
        std::string path = request.path;
        std::regex user_id_regex(R"(/api/users/([^/]+)/permissions)");
        std::smatch matches;

        if (!std::regex_match(path, matches, user_id_regex)) {
            return createErrorResponse(400, "invalid_request", "Invalid path format");
        }

        std::string user_id = matches[1].str();

        // Get query parameters for project_id (required for permissions)
        std::string project_id;
        auto query_pos = request.path.find('?');
        if (query_pos != std::string::npos) {
            std::string query = request.path.substr(query_pos + 1);
            if (query.find("project_id=") == 0) {
                project_id = query.substr(11);
            }
        }

        if (project_id.empty()) {
            return createErrorResponse(400, "invalid_request", "project_id parameter is required");
        }

        // Get user permissions for the project
        auto permissions = rbac_manager_->getUserProjectPermissions(user_id, project_id);

        json response;
        response["user_id"] = user_id;
        response["project_id"] = project_id;
        response["permissions"] = permissions;

        return createSuccessResponse(response.dump());

    } catch (const std::exception& e) {
        return createErrorResponse(500, "server_error", "Error retrieving user permissions: " + std::string(e.what()));
    }
}

HttpServer::HttpResponse HttpServer::handleGetProjectUsers(const HttpRequest& request) {
    try {
        // Extract project_id from path
        std::string path = request.path;
        std::regex project_id_regex(R"(/api/projects/([^/]+)/users)");
        std::smatch matches;

        if (!std::regex_match(path, matches, project_id_regex)) {
            return createErrorResponse(400, "invalid_request", "Invalid path format");
        }

        std::string project_id = matches[1].str();

        // Get project users (this would require a new method in RBACManager)
        json response;
        response["project_id"] = project_id;
        response["users"] = json::array();
        response["message"] = "Project users endpoint - implementation pending";

        return createSuccessResponse(response.dump());

    } catch (const std::exception& e) {
        return createErrorResponse(500, "server_error", "Error retrieving project users: " + std::string(e.what()));
    }
}

HttpServer::HttpResponse HttpServer::handleGetOrganizations(const HttpRequest& request) {
    try {
        // Get all organizations
        auto organizations = rbac_manager_->listOrganizations(true);

        json response;
        response["organizations"] = json::array();

        for (const auto& org : organizations) {
            json org_obj;
            org_obj["org_id"] = org.org_id;
            org_obj["org_name"] = org.org_name;
            org_obj["org_domain"] = org.org_domain;
            org_obj["is_active"] = org.is_active;
            org_obj["created_at"] = std::chrono::duration_cast<std::chrono::seconds>(
                org.created_at.time_since_epoch()).count();
            response["organizations"].push_back(org_obj);
        }

        return createSuccessResponse(response.dump());

    } catch (const std::exception& e) {
        return createErrorResponse(500, "server_error", "Error retrieving organizations: " + std::string(e.what()));
    }
}

HttpServer::HttpResponse HttpServer::handleGetProjects(const HttpRequest& request) {
    try {
        // Get query parameters for organization_id (optional)
        std::string org_id;
        auto query_pos = request.path.find('?');
        if (query_pos != std::string::npos) {
            std::string query = request.path.substr(query_pos + 1);
            if (query.find("org_id=") == 0) {
                org_id = query.substr(7);
            }
        }

        json response;
        response["projects"] = json::array();

        if (!org_id.empty()) {
            // Get projects for specific organization
            auto projects = rbac_manager_->listProjectsByOrganization(org_id, true);
            response["org_id"] = org_id;

            for (const auto& project : projects) {
                json project_obj;
                project_obj["project_id"] = project.project_id;
                project_obj["project_name"] = project.project_name;
                project_obj["project_description"] = project.project_description;
                project_obj["org_id"] = project.org_id;
                project_obj["is_active"] = project.is_active;
                project_obj["created_at"] = std::chrono::duration_cast<std::chrono::seconds>(
                    project.created_at.time_since_epoch()).count();
                response["projects"].push_back(project_obj);
            }
        } else {
            // Return message to specify org_id
            response["message"] = "Use org_id parameter to get projects for specific organization";
        }

        return createSuccessResponse(response.dump());

    } catch (const std::exception& e) {
        return createErrorResponse(500, "server_error", "Error retrieving projects: " + std::string(e.what()));
    }
}

HttpServer::HttpResponse HttpServer::handleRefreshRequest(const HttpRequest& request) {
    try {
        auto params = parseJsonBody(request.body);

        std::string refresh_token = params["refresh_token"];
        std::string grant_type = (params.find("grant_type") != params.end()) ? params["grant_type"] : "refresh_token";

        if (refresh_token.empty()) {
            return createErrorResponse(400, "invalid_request", "Missing refresh_token");
        }

        if (grant_type != "refresh_token") {
            return createErrorResponse(400, "unsupported_grant_type", "Only refresh_token grant type is supported");
        }

        // Refresh the token
        auto token_pair = jwt_manager_->refreshToken(refresh_token);

        if (!token_pair.success) {
            return createErrorResponse(400, "invalid_grant", token_pair.error_message);
        }

        // Create OAuth 2.0 token response
        json response;
        response["access_token"] = token_pair.access_token.token;
        response["refresh_token"] = token_pair.refresh_token.token;
        response["token_type"] = "Bearer";
        response["expires_in"] = std::chrono::duration_cast<std::chrono::seconds>(
            token_pair.access_token.expires_at - std::chrono::system_clock::now()).count();
        response["scope"] = "read write";

        return createSuccessResponse(response.dump());

    } catch (const std::exception& e) {
        return createErrorResponse(400, "invalid_request", "Invalid request format: " + std::string(e.what()));
    }
}

HttpServer::HttpResponse HttpServer::handleValidateRequest(const HttpRequest& request) {
    try {
        std::string token = extractBearerToken(request);

        if (token.empty()) {
            return createErrorResponse(401, "invalid_token", "Missing or invalid Authorization header");
        }

        // Validate the token
        auto validation = jwt_manager_->validateToken(token);

        if (!validation.valid) {
            return createErrorResponse(401, "invalid_token", validation.error_message);
        }

        // Create validation response
        json response;
        response["valid"] = true;
        response["user_id"] = validation.user_id;
        response["scopes"] = validation.scopes;
        response["expires_at"] = std::chrono::duration_cast<std::chrono::seconds>(
            validation.expires_at.time_since_epoch()).count();

        return createSuccessResponse(response.dump());

    } catch (const std::exception& e) {
        return createErrorResponse(400, "invalid_request", "Invalid request format: " + std::string(e.what()));
    }
}

HttpServer::HttpResponse HttpServer::handleRevokeRequest(const HttpRequest& request) {
    try {
        auto params = parseJsonBody(request.body);

        std::string token = params["token"];

        if (token.empty()) {
            return createErrorResponse(400, "invalid_request", "Missing token parameter");
        }

        // Revoke the token
        bool revoked = jwt_manager_->revokeToken(token);

        if (!revoked) {
            return createErrorResponse(400, "invalid_token", "Token could not be revoked");
        }

        // Create revocation response (empty body for success)
        HttpResponse response(200);
        response.body = "{}";

        return response;

    } catch (const std::exception& e) {
        return createErrorResponse(400, "invalid_request", "Invalid request format: " + std::string(e.what()));
    }
}

HttpServer::HttpResponse HttpServer::handleHealthCheck(const HttpRequest& request) {
    json response;
    response["status"] = "healthy";
    response["service"] = "auth-service";
    response["version"] = "1.0.0";
    response["oauth2_endpoints"] = {
        "/oauth/token",
        "/oauth/refresh",
        "/oauth/validate",
        "/oauth/revoke"
    };
    response["timestamp"] = std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();

    return createSuccessResponse(response.dump());
}

HttpServer::HttpResponse HttpServer::createErrorResponse(int status_code, const std::string& error,
                                                        const std::string& description) {
    HttpResponse response(status_code);

    json error_json;
    error_json["error"] = error;
    if (!description.empty()) {
        error_json["error_description"] = description;
    }

    response.body = error_json.dump();
    return response;
}

HttpServer::HttpResponse HttpServer::createSuccessResponse(const std::string& json_body) {
    HttpResponse response(200);
    response.body = json_body;
    return response;
}

std::string HttpServer::extractBearerToken(const HttpRequest& request) {
    auto auth_it = request.headers.find("Authorization");
    if (auth_it == request.headers.end()) {
        return "";
    }

    const std::string& auth_header = auth_it->second;
    const std::string bearer_prefix = "Bearer ";

    if (auth_header.substr(0, bearer_prefix.length()) == bearer_prefix) {
        return auth_header.substr(bearer_prefix.length());
    }

    return "";
}

std::unordered_map<std::string, std::string> HttpServer::parseJsonBody(const std::string& body) {
    std::unordered_map<std::string, std::string> params;

    if (body.empty()) {
        return params;
    }

    try {
        json json_body = json::parse(body);

        for (auto& [key, value] : json_body.items()) {
            if (value.is_string()) {
                params[key] = value.get<std::string>();
            } else {
                params[key] = value.dump();
            }
        }
    } catch (const std::exception& e) {
        // If JSON parsing fails, return empty params
        std::cerr << "JSON parsing error: " << e.what() << std::endl;
    }

    return params;
}

HttpServer::HttpRequest HttpServer::parseRequest(const std::string& raw_request) {
    HttpRequest request;

    // This is a simplified parser for demonstration
    // In a real implementation, you'd use a proper HTTP library

    std::istringstream stream(raw_request);
    std::string line;

    // Parse request line
    if (std::getline(stream, line)) {
        std::istringstream request_line(line);
        request_line >> request.method >> request.path;
    }

    // Parse headers
    while (std::getline(stream, line) && !line.empty() && line != "\r") {
        size_t colon_pos = line.find(':');
        if (colon_pos != std::string::npos) {
            std::string key = line.substr(0, colon_pos);
            std::string value = line.substr(colon_pos + 1);

            // Trim whitespace
            key.erase(0, key.find_first_not_of(" \t"));
            key.erase(key.find_last_not_of(" \t") + 1);
            value.erase(0, value.find_first_not_of(" \t"));
            value.erase(value.find_last_not_of(" \t") + 1);

            request.headers[key] = value;
        }
    }

    // Parse body
    std::string body_line;
    while (std::getline(stream, body_line)) {
        request.body += body_line + "\n";
    }

    return request;
}

std::string HttpServer::generateResponse(const HttpResponse& response) {
    std::ostringstream response_stream;

    // Status line
    response_stream << "HTTP/1.1 " << response.status_code << " ";

    switch (response.status_code) {
        case 200: response_stream << "OK"; break;
        case 400: response_stream << "Bad Request"; break;
        case 401: response_stream << "Unauthorized"; break;
        case 404: response_stream << "Not Found"; break;
        case 500: response_stream << "Internal Server Error"; break;
        default: response_stream << "Unknown"; break;
    }

    response_stream << "\r\n";

    // Headers
    for (const auto& [key, value] : response.headers) {
        response_stream << key << ": " << value << "\r\n";
    }

    // Content-Length header
    response_stream << "Content-Length: " << response.body.length() << "\r\n";

    // Empty line before body
    response_stream << "\r\n";

    // Body
    response_stream << response.body;

    return response_stream.str();
}

std::string HttpServer::extractClientIP(const HttpRequest& request) {
    // Check for forwarded IP headers first (from nginx proxy)
    auto it = request.headers.find("X-Real-IP");
    if (it != request.headers.end() && !it->second.empty()) {
        return it->second;
    }

    it = request.headers.find("X-Forwarded-For");
    if (it != request.headers.end() && !it->second.empty()) {
        // X-Forwarded-For can contain multiple IPs, take the first one
        std::string forwarded = it->second;
        size_t comma_pos = forwarded.find(',');
        if (comma_pos != std::string::npos) {
            return forwarded.substr(0, comma_pos);
        }
        return forwarded;
    }

    // Fallback to localhost for local requests
    return "127.0.0.1";
}

HttpServer::HttpResponse HttpServer::handleRateLimit(const std::string& client_ip, const std::string& endpoint) {
    auto result = rate_limiter_->checkLimit(client_ip, endpoint);

    if (!result.allowed) {
        json error_response;
        error_response["error"] = "rate_limit_exceeded";
        error_response["error_description"] = result.message;
        error_response["retry_after"] = std::chrono::duration_cast<std::chrono::seconds>(
            result.reset_time - std::chrono::system_clock::now()).count();

        HttpResponse response(429); // Too Many Requests
        response.headers["Retry-After"] = std::to_string(
            std::chrono::duration_cast<std::chrono::seconds>(
                result.reset_time - std::chrono::system_clock::now()).count());
        response.headers["X-RateLimit-Limit"] = "varies by endpoint";
        response.headers["X-RateLimit-Remaining"] = "0";
        response.body = error_response.dump();

        std::cout << "Rate limit exceeded for " << client_ip << " on " << endpoint
                  << ": " << result.message << std::endl;

        return response;
    }

    // Add rate limit headers to successful responses
    HttpResponse response(200);
    response.headers["X-RateLimit-Remaining"] = std::to_string(result.remaining_requests);
    response.body = "{}"; // Empty JSON response for rate limit check

    return response;
}

// ============================================================================
// Admin User Management API Endpoints
// ============================================================================

HttpServer::HttpResponse HttpServer::handleGetAllUsers(const HttpRequest& request) {
    try {
        // TODO: Add authorization check - only admin users should access this

        // Get all users from database
        auto users = user_manager_->getAllUsers();

        json response;
        response["users"] = json::array();

        for (const auto& user : users) {
            json user_obj;
            user_obj["user_id"] = user.user_id;
            user_obj["username"] = user.username;
            user_obj["email"] = user.email;
            user_obj["first_name"] = user.first_name;
            user_obj["last_name"] = user.last_name;
            user_obj["is_active"] = user.is_active;
            user_obj["email_verified"] = user.email_verified;
            user_obj["is_system_admin"] = user.is_system_admin;
            user_obj["created_at"] = std::chrono::duration_cast<std::chrono::seconds>(
                user.created_at.time_since_epoch()).count();
            response["users"].push_back(user_obj);
        }

        return createSuccessResponse(response.dump());

    } catch (const std::exception& e) {
        return createErrorResponse(500, "server_error", "Error retrieving users: " + std::string(e.what()));
    }
}

HttpServer::HttpResponse HttpServer::handleCreateUser(const HttpRequest& request) {
    try {
        // TODO: Add authorization check - only admin users should access this

        // Parse request body
        auto params = parseJsonBody(request.body);

        std::string username = params["username"];
        std::string email = params["email"];
        std::string password = params["password"];
        std::string first_name = params["first_name"];
        std::string last_name = params["last_name"];

        if (username.empty() || password.empty()) {
            return createErrorResponse(400, "invalid_request", "Username and password are required");
        }

        // Create user
        auto result = user_manager_->createUser(username, email, password, first_name, last_name);

        if (!result.success) {
            return createErrorResponse(400, "user_creation_failed", result.error_message);
        }

        json response;
        response["success"] = true;
        response["user_id"] = result.user_id;
        response["username"] = username;
        response["email"] = email;
        response["message"] = "User created successfully";

        std::cout << "User created: " << username << " (ID: " << result.user_id << ")" << std::endl;

        return createSuccessResponse(response.dump());

    } catch (const std::exception& e) {
        return createErrorResponse(500, "server_error", "Error creating user: " + std::string(e.what()));
    }
}

HttpServer::HttpResponse HttpServer::handleUpdateUser(const HttpRequest& request) {
    try {
        // TODO: Add authorization check - only admin users should access this

        // Extract user_id from path
        std::string path = request.path;
        std::regex user_id_regex(R"(/api/admin/users/([^/]+))");
        std::smatch matches;

        if (!std::regex_match(path, matches, user_id_regex)) {
            return createErrorResponse(400, "invalid_request", "Invalid path format");
        }

        std::string user_id = matches[1].str();

        // Parse request body
        auto params = parseJsonBody(request.body);

        // TODO: Implement user update functionality in UserManager
        json response;
        response["success"] = true;
        response["user_id"] = user_id;
        response["message"] = "User update endpoint - implementation pending";

        return createSuccessResponse(response.dump());

    } catch (const std::exception& e) {
        return createErrorResponse(500, "server_error", "Error updating user: " + std::string(e.what()));
    }
}

HttpServer::HttpResponse HttpServer::handleDeleteUser(const HttpRequest& request) {
    try {
        // TODO: Add authorization check - only admin users should access this

        // Extract user_id from path
        std::string path = request.path;
        std::regex user_id_regex(R"(/api/admin/users/([^/]+))");
        std::smatch matches;

        if (!std::regex_match(path, matches, user_id_regex)) {
            return createErrorResponse(400, "invalid_request", "Invalid path format");
        }

        std::string user_id = matches[1].str();

        // TODO: Implement user deletion functionality in UserManager
        json response;
        response["success"] = true;
        response["user_id"] = user_id;
        response["message"] = "User deletion endpoint - implementation pending";

        return createSuccessResponse(response.dump());

    } catch (const std::exception& e) {
        return createErrorResponse(500, "server_error", "Error deleting user: " + std::string(e.what()));
    }
}

// ============================================================================
// Admin Role Management API Endpoints
// ============================================================================

HttpServer::HttpResponse HttpServer::handleGetAllRoles(const HttpRequest& request) {
    try {
        // TODO: Add authorization check - only admin users should access this

        // Get all roles from RBAC manager
        auto roles = rbac_manager_->listRoles(true); // true = include inactive

        json response;
        response["roles"] = json::array();

        for (const auto& role : roles) {
            json role_obj;
            role_obj["role_id"] = role.role_id;
            role_obj["role_name"] = role.role_name;
            role_obj["description"] = role.description;
            role_obj["is_system_role"] = role.is_system_role;
            role_obj["is_active"] = role.is_active;
            role_obj["created_at"] = std::chrono::duration_cast<std::chrono::seconds>(
                role.created_at.time_since_epoch()).count();
            response["roles"].push_back(role_obj);
        }

        return createSuccessResponse(response.dump());

    } catch (const std::exception& e) {
        return createErrorResponse(500, "server_error", "Error retrieving roles: " + std::string(e.what()));
    }
}

HttpServer::HttpResponse HttpServer::handleCreateRole(const HttpRequest& request) {
    try {
        // TODO: Add authorization check - only admin users should access this

        // Parse request body
        auto params = parseJsonBody(request.body);

        std::string role_name = params["role_name"];
        std::string description = params["description"];
        bool is_system_role = (params.find("is_system_role") != params.end() &&
                              params["is_system_role"] == "true");

        if (role_name.empty()) {
            return createErrorResponse(400, "invalid_request", "Role name is required");
        }

        // Create role
        auto result = rbac_manager_->createRole(role_name, description, is_system_role);

        if (result.empty()) {
            return createErrorResponse(400, "role_creation_failed", "Failed to create role");
        }

        json response;
        response["success"] = true;
        response["role_id"] = result;
        response["role_name"] = role_name;
        response["description"] = description;
        response["is_system_role"] = is_system_role;
        response["message"] = "Role created successfully";

        std::cout << "Role created: " << role_name << " (ID: " << result << ")" << std::endl;

        return createSuccessResponse(response.dump());

    } catch (const std::exception& e) {
        return createErrorResponse(500, "server_error", "Error creating role: " + std::string(e.what()));
    }
}

HttpServer::HttpResponse HttpServer::handleUpdateRole(const HttpRequest& request) {
    try {
        // TODO: Add authorization check - only admin users should access this

        // Extract role_id from path
        std::string path = request.path;
        std::regex role_id_regex(R"(/api/admin/roles/([^/]+))");
        std::smatch matches;

        if (!std::regex_match(path, matches, role_id_regex)) {
            return createErrorResponse(400, "invalid_request", "Invalid path format");
        }

        std::string role_id = matches[1].str();

        // Parse request body
        auto params = parseJsonBody(request.body);

        // TODO: Implement role update functionality in RBACManager
        json response;
        response["success"] = true;
        response["role_id"] = role_id;
        response["message"] = "Role update endpoint - implementation pending";

        return createSuccessResponse(response.dump());

    } catch (const std::exception& e) {
        return createErrorResponse(500, "server_error", "Error updating role: " + std::string(e.what()));
    }
}

HttpServer::HttpResponse HttpServer::handleDeleteRole(const HttpRequest& request) {
    try {
        // TODO: Add authorization check - only admin users should access this

        // Extract role_id from path
        std::string path = request.path;
        std::regex role_id_regex(R"(/api/admin/roles/([^/]+))");
        std::smatch matches;

        if (!std::regex_match(path, matches, role_id_regex)) {
            return createErrorResponse(400, "invalid_request", "Invalid path format");
        }

        std::string role_id = matches[1].str();

        // TODO: Implement role deletion functionality in RBACManager
        json response;
        response["success"] = true;
        response["role_id"] = role_id;
        response["message"] = "Role deletion endpoint - implementation pending";

        return createSuccessResponse(response.dump());

    } catch (const std::exception& e) {
        return createErrorResponse(500, "server_error", "Error deleting role: " + std::string(e.what()));
    }
}

// ============================================================================
// Admin Permission Management API Endpoints
// ============================================================================

HttpServer::HttpResponse HttpServer::handleGetAllPermissions(const HttpRequest& request) {
    try {
        // TODO: Add authorization check - only admin users should access this

        // Get all permissions from RBAC manager
        auto permissions = rbac_manager_->listPermissions();

        json response;
        response["permissions"] = json::array();

        for (const auto& permission : permissions) {
            json perm_obj;
            perm_obj["permission_id"] = permission.permission_id;
            perm_obj["permission_name"] = permission.permission_name;
            perm_obj["description"] = permission.description;
            perm_obj["created_at"] = std::chrono::duration_cast<std::chrono::seconds>(
                permission.created_at.time_since_epoch()).count();
            response["permissions"].push_back(perm_obj);
        }

        return createSuccessResponse(response.dump());

    } catch (const std::exception& e) {
        return createErrorResponse(500, "server_error", "Error retrieving permissions: " + std::string(e.what()));
    }
}

HttpServer::HttpResponse HttpServer::handleCreatePermission(const HttpRequest& request) {
    try {
        // TODO: Add authorization check - only admin users should access this

        // Parse request body
        auto params = parseJsonBody(request.body);

        std::string permission_name = params["permission_name"];
        std::string description = params["description"];

        if (permission_name.empty()) {
            return createErrorResponse(400, "invalid_request", "Permission name is required");
        }

        // Create permission
        auto result = rbac_manager_->createPermission(permission_name, description);

        if (result.empty()) {
            return createErrorResponse(400, "permission_creation_failed", "Failed to create permission");
        }

        json response;
        response["success"] = true;
        response["permission_id"] = result;
        response["permission_name"] = permission_name;
        response["description"] = description;
        response["message"] = "Permission created successfully";

        std::cout << "Permission created: " << permission_name << " (ID: " << result << ")" << std::endl;

        return createSuccessResponse(response.dump());

    } catch (const std::exception& e) {
        return createErrorResponse(500, "server_error", "Error creating permission: " + std::string(e.what()));
    }
}

HttpServer::HttpResponse HttpServer::handleUpdatePermission(const HttpRequest& request) {
    try {
        // TODO: Add authorization check - only admin users should access this

        // Extract permission_id from path
        std::string path = request.path;
        std::regex perm_id_regex(R"(/api/admin/permissions/([^/]+))");
        std::smatch matches;

        if (!std::regex_match(path, matches, perm_id_regex)) {
            return createErrorResponse(400, "invalid_request", "Invalid path format");
        }

        std::string permission_id = matches[1].str();

        // Parse request body
        auto params = parseJsonBody(request.body);

        // TODO: Implement permission update functionality in RBACManager
        json response;
        response["success"] = true;
        response["permission_id"] = permission_id;
        response["message"] = "Permission update endpoint - implementation pending";

        return createSuccessResponse(response.dump());

    } catch (const std::exception& e) {
        return createErrorResponse(500, "server_error", "Error updating permission: " + std::string(e.what()));
    }
}

HttpServer::HttpResponse HttpServer::handleDeletePermission(const HttpRequest& request) {
    try {
        // TODO: Add authorization check - only admin users should access this

        // Extract permission_id from path
        std::string path = request.path;
        std::regex perm_id_regex(R"(/api/admin/permissions/([^/]+))");
        std::smatch matches;

        if (!std::regex_match(path, matches, perm_id_regex)) {
            return createErrorResponse(400, "invalid_request", "Invalid path format");
        }

        std::string permission_id = matches[1].str();

        // TODO: Implement permission deletion functionality in RBACManager
        json response;
        response["success"] = true;
        response["permission_id"] = permission_id;
        response["message"] = "Permission deletion endpoint - implementation pending";

        return createSuccessResponse(response.dump());

    } catch (const std::exception& e) {
        return createErrorResponse(500, "server_error", "Error deleting permission: " + std::string(e.what()));
    }
}

// ============================================================================
// Admin User Assignment API Endpoints
// ============================================================================

HttpServer::HttpResponse HttpServer::handleGetUserAssignments(const HttpRequest& request) {
    try {
        // TODO: Add authorization check - only admin users should access this

        // TODO: Implement user assignment listing functionality
        json response;
        response["assignments"] = json::array();
        response["message"] = "User assignments endpoint - implementation pending";

        return createSuccessResponse(response.dump());

    } catch (const std::exception& e) {
        return createErrorResponse(500, "server_error", "Error retrieving user assignments: " + std::string(e.what()));
    }
}

HttpServer::HttpResponse HttpServer::handleCreateUserAssignment(const HttpRequest& request) {
    try {
        // TODO: Add authorization check - only admin users should access this

        // Parse request body
        auto params = parseJsonBody(request.body);

        std::string user_id = params["user_id"];
        std::string role_id = params["role_id"];
        std::string project_id = params["project_id"];

        if (user_id.empty() || role_id.empty() || project_id.empty()) {
            return createErrorResponse(400, "invalid_request", "user_id, role_id, and project_id are required");
        }

        // Assign role to user for project
        bool result = rbac_manager_->assignUserProjectRole(user_id, project_id, role_id);

        if (!result) {
            return createErrorResponse(400, "assignment_failed", "Failed to assign role to user");
        }

        json response;
        response["success"] = true;
        response["user_id"] = user_id;
        response["role_id"] = role_id;
        response["project_id"] = project_id;
        response["message"] = "User assignment created successfully";

        std::cout << "User assignment created: User " << user_id
                  << " assigned role " << role_id
                  << " in project " << project_id << std::endl;

        return createSuccessResponse(response.dump());

    } catch (const std::exception& e) {
        return createErrorResponse(500, "server_error", "Error creating user assignment: " + std::string(e.what()));
    }
}

HttpServer::HttpResponse HttpServer::handleDeleteUserAssignment(const HttpRequest& request) {
    try {
        // TODO: Add authorization check - only admin users should access this

        // Extract assignment_id from path
        std::string path = request.path;
        std::regex assignment_id_regex(R"(/api/admin/user-assignments/([^/]+))");
        std::smatch matches;

        if (!std::regex_match(path, matches, assignment_id_regex)) {
            return createErrorResponse(400, "invalid_request", "Invalid path format");
        }

        std::string assignment_id = matches[1].str();

        // TODO: Implement user assignment deletion functionality
        json response;
        response["success"] = true;
        response["assignment_id"] = assignment_id;
        response["message"] = "User assignment deletion endpoint - implementation pending";

        return createSuccessResponse(response.dump());

    } catch (const std::exception& e) {
        return createErrorResponse(500, "server_error", "Error deleting user assignment: " + std::string(e.what()));
    }
}
