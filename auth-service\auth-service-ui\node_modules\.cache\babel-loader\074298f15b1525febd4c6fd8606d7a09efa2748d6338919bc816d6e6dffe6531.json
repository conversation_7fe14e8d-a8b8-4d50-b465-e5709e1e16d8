{"ast": null, "code": "import * as React from 'react';\nvar syncFallback = function syncFallback(create) {\n  return create();\n};\nvar useInsertionEffect = React['useInsertion' + 'Effect'] ? React['useInsertion' + 'Effect'] : false;\nvar useInsertionEffectAlwaysWithSyncFallback = useInsertionEffect || syncFallback;\nvar useInsertionEffectWithLayoutFallback = useInsertionEffect || React.useLayoutEffect;\nexport { useInsertionEffectAlwaysWithSyncFallback, useInsertionEffectWithLayoutFallback };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}