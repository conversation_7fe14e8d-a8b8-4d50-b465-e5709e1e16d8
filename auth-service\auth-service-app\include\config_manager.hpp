﻿#pragma once
#include <string>
#include <vector>
#include <optional>
#include <nlohmann/json.hpp>

/**
 * @file config_manager.hpp
 * @brief Comprehensive configuration management for auth-service
 *
 * This class provides access to all configuration settings for the auth-service
 * application, including database, server, OAuth 2.0, RBAC, security, and
 * operational settings.
 *
 * <AUTHOR> Service Team
 * @version 2.0.0
 * @date 2025-07-17
 */

class ConfigManager {
public:
    explicit ConfigManager(const std::string& config_file);

    // ========================================================================
    // Database Configuration
    // ========================================================================
    std::string get_database_host() const;
    int get_database_port() const;
    std::string get_database_name() const;
    std::string get_database_user() const;
    std::string get_database_password() const;

    // Database connection pool settings
    int get_database_min_connections() const;
    int get_database_max_connections() const;
    int get_database_connection_timeout() const;
    int get_database_idle_timeout() const;

    // Database SSL settings
    bool get_database_ssl_enabled() const;
    std::string get_database_ssl_mode() const;
    std::string get_database_ssl_cert_file() const;
    std::string get_database_ssl_key_file() const;

    // ========================================================================
    // Server Configuration
    // ========================================================================
    int get_server_port() const;
    std::string get_server_host() const;
    std::string get_log_level() const;
    int get_server_worker_threads() const;
    int get_server_max_request_size() const;
    int get_server_request_timeout() const;
    int get_server_keep_alive_timeout() const;

    // Server CORS settings
    bool get_server_cors_enabled() const;
    std::vector<std::string> get_server_cors_allowed_origins() const;
    std::vector<std::string> get_server_cors_allowed_methods() const;
    std::vector<std::string> get_server_cors_allowed_headers() const;
    bool get_server_cors_allow_credentials() const;
    int get_server_cors_max_age() const;

    // Server SSL settings
    bool get_server_ssl_enabled() const;
    std::string get_server_ssl_cert_file() const;
    std::string get_server_ssl_key_file() const;
    std::vector<std::string> get_server_ssl_protocols() const;

    // ========================================================================
    // OAuth 2.0 JWT Configuration
    // ========================================================================
    std::string get_jwt_secret() const;
    std::string get_jwt_algorithm() const;
    std::string get_jwt_issuer() const;
    std::string get_jwt_audience() const;
    int get_jwt_access_token_expiry() const;  // seconds
    int get_jwt_refresh_token_expiry() const; // seconds
    int get_jwt_cleanup_interval() const;     // seconds
    int get_jwt_max_tokens_per_user() const;

    // ========================================================================
    // OAuth 2.0 Argon2 Configuration
    // ========================================================================
    int get_argon2_memory_cost() const;       // KB
    int get_argon2_time_cost() const;         // iterations
    int get_argon2_parallelism() const;       // threads
    int get_argon2_salt_length() const;       // bytes
    int get_argon2_hash_length() const;       // bytes

    // ========================================================================
    // OAuth 2.0 Session Configuration
    // ========================================================================
    int get_session_timeout() const;          // seconds
    int get_session_cleanup_interval() const; // seconds
    int get_max_sessions_per_user() const;
    bool get_session_secure_cookies() const;
    std::string get_session_same_site() const;

    // ========================================================================
    // Rate Limiting Configuration
    // ========================================================================
    bool get_rate_limiting_enabled() const;

    // Login rate limiting
    int get_rate_limit_login_max_attempts() const;
    int get_rate_limit_login_window_minutes() const;
    int get_rate_limit_login_lockout_minutes() const;

    // API rate limiting
    int get_rate_limit_api_max_requests() const;
    int get_rate_limit_api_window_minutes() const;

    // Token generation rate limiting
    int get_rate_limit_token_max_tokens() const;
    int get_rate_limit_token_window_minutes() const;

    // ========================================================================
    // Multi-tenant Configuration
    // ========================================================================
    bool get_multi_tenant_enabled() const;
    std::string get_multi_tenant_default_organization() const;
    std::string get_multi_tenant_default_project() const;
    bool get_multi_tenant_auto_create_projects() const;
    int get_multi_tenant_max_organizations() const;
    int get_multi_tenant_max_projects_per_org() const;

    // ========================================================================
    // RBAC Configuration
    // ========================================================================
    bool get_rbac_enabled() const;
    bool get_rbac_cache_permissions() const;
    int get_rbac_permission_cache_ttl() const;
    bool get_rbac_role_inheritance() const;
    std::vector<std::string> get_rbac_default_roles() const;
    int get_rbac_max_roles_per_user() const;
    bool get_rbac_audit_permissions() const;

private:
    nlohmann::json config_;
    void load_config(const std::string& config_file);
    void ensure_defaults();

    // Helper methods for safe JSON access
    template<typename T>
    T get_value(const std::vector<std::string>& path, const T& default_value) const;

    bool has_path(const std::vector<std::string>& path) const;
};
