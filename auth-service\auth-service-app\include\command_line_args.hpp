#pragma once

#include <string>
#include <vector>
#include <optional>
#include <boost/program_options.hpp>

/**
 * @file command_line_args.hpp
 * @brief Command line argument parsing and management for auth-service
 * 
 * This module provides a clean interface for handling all command line arguments
 * for the auth-service application. It encapsulates Boost Program Options and
 * provides a structured way to access parsed arguments.
 * 
 * <AUTHOR> Service Team
 * @version 1.0.0
 * @date 2025-07-17
 */

namespace AuthService {

/**
 * @brief Log level enumeration for type-safe log level handling
 */
enum class LogLevel {
    DEBUG,
    INFO,
    WARN,
    ERROR
};

/**
 * @brief Structure to hold all parsed command line arguments
 * 
 * This structure provides a clean interface to access all command line
 * arguments without having to deal with boost::program_options directly
 * in the main application code.
 */
struct CommandLineArgs {
    // Basic application control
    bool show_help = false;
    bool show_version = false;
    bool dry_run = false;
    bool test_config = false;
    bool daemon_mode = false;
    
    // Configuration and runtime options
    std::string config_file = "/etc/auth-service/auth-service.conf";
    int port = 8082;
    LogLevel log_level = LogLevel::INFO;
    std::optional<std::string> log_file;
    std::optional<std::string> pid_file;
    
    // Database override options
    std::optional<std::string> db_host;
    std::optional<int> db_port;
    std::optional<std::string> db_name;
    std::optional<std::string> db_user;
    
    // Security and performance options
    std::optional<int> max_connections;
    std::optional<int> worker_threads;
    bool enable_cors = true;
    std::optional<std::string> cors_origin;
    
    // Development and debugging options
    bool verbose = false;
    bool debug_mode = false;
    bool enable_metrics = true;
    std::optional<int> metrics_port;
};

/**
 * @brief Command line argument parser and manager
 * 
 * This class encapsulates all command line argument parsing logic and provides
 * a clean interface for the main application. It handles argument validation,
 * help text generation, and provides structured access to parsed arguments.
 */
class CommandLineParser {
public:
    /**
     * @brief Constructor - sets up argument definitions
     */
    CommandLineParser();
    
    /**
     * @brief Destructor
     */
    ~CommandLineParser() = default;
    
    /**
     * @brief Parse command line arguments
     * 
     * @param argc Argument count from main()
     * @param argv Argument vector from main()
     * @return CommandLineArgs structure with parsed arguments
     * @throws std::runtime_error if parsing fails or invalid arguments provided
     */
    CommandLineArgs parse(int argc, char* argv[]);
    
    /**
     * @brief Get help text for all available options
     * 
     * @return String containing formatted help text
     */
    std::string getHelpText() const;
    
    /**
     * @brief Get version information
     * 
     * @return String containing version and build information
     */
    std::string getVersionInfo() const;
    
    /**
     * @brief Validate parsed arguments for consistency
     * 
     * @param args Parsed command line arguments
     * @return true if arguments are valid, false otherwise
     */
    bool validateArguments(const CommandLineArgs& args) const;
    
    /**
     * @brief Convert LogLevel enum to string
     * 
     * @param level LogLevel enum value
     * @return String representation of log level
     */
    static std::string logLevelToString(LogLevel level);
    
    /**
     * @brief Convert string to LogLevel enum
     * 
     * @param level_str String representation of log level
     * @return LogLevel enum value
     * @throws std::invalid_argument if string is not a valid log level
     */
    static LogLevel stringToLogLevel(const std::string& level_str);

private:
    boost::program_options::options_description desc_;
    boost::program_options::variables_map vm_;
    
    /**
     * @brief Set up all command line option definitions
     */
    void setupOptions();
    
    /**
     * @brief Convert boost::program_options variables_map to CommandLineArgs
     * 
     * @return CommandLineArgs structure with parsed values
     */
    CommandLineArgs extractArgs() const;
    
    /**
     * @brief Validate individual argument values
     * 
     * @param args CommandLineArgs to validate
     * @return Vector of validation error messages (empty if valid)
     */
    std::vector<std::string> getValidationErrors(const CommandLineArgs& args) const;
};

/**
 * @brief Exception class for command line argument errors
 */
class CommandLineException : public std::runtime_error {
public:
    explicit CommandLineException(const std::string& message) 
        : std::runtime_error("Command line error: " + message) {}
};

/**
 * @brief Exception class for argument validation errors
 */
class ArgumentValidationException : public CommandLineException {
public:
    explicit ArgumentValidationException(const std::vector<std::string>& errors);
    
    const std::vector<std::string>& getErrors() const { return errors_; }

private:
    std::vector<std::string> errors_;
};

} // namespace AuthService
