# Deploy Updated HTML UI to Auth-Dev Server
# This script deploys the updated login page with professional icons to the auth-dev server

param(
    [string]$Environment = "auth-dev",
    [switch]$Force = $false
)

Write-Host "Deploying Updated HTML UI to Auth-Dev Server..." -ForegroundColor Cyan
Write-Host ""

# Configuration
$LocalUIPath = "D:\Coding_Projects\auth-service\auth-service-ui\html"
$RemoteUIPath = "/opt/auth-service-ui/html"
$RemoteHost = "auth-dev.chcit.org"
$RemoteUser = "btaylor-admin"
$SSHKeyPath = "C:\Users\<USER>\.ssh\id_rsa"

# Files to deploy
$FilesToDeploy = @(
    "index.html",
    "admin.html", 
    "dashboard.html"
)

Write-Host "Configuration:" -ForegroundColor Yellow
Write-Host "   Local Path: $LocalUIPath"
Write-Host "   Remote Host: $RemoteHost"
Write-Host "   Remote Path: $RemoteUIPath"
Write-Host "   Files: $($FilesToDeploy -join ', ')"
Write-Host ""

# Verify local files exist
Write-Host "🔍 Verifying local files..." -ForegroundColor Yellow
$MissingFiles = @()
foreach ($file in $FilesToDeploy) {
    $localFile = Join-Path $LocalUIPath $file
    if (-not (Test-Path $localFile)) {
        $MissingFiles += $file
        Write-Host "   ❌ Missing: $file" -ForegroundColor Red
    } else {
        $fileSize = (Get-Item $localFile).Length
        Write-Host "   ✅ Found: $file ($fileSize bytes)" -ForegroundColor Green
    }
}

if ($MissingFiles.Count -gt 0) {
    Write-Host ""
    Write-Host "❌ Missing files found. Cannot proceed with deployment." -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "✅ All local files verified successfully!" -ForegroundColor Green
Write-Host ""

# Confirm deployment
if (-not $Force) {
    $confirm = Read-Host "🚀 Deploy updated HTML UI to $RemoteHost? (y/N)"
    if ($confirm -ne 'y' -and $confirm -ne 'Y') {
        Write-Host "❌ Deployment cancelled by user." -ForegroundColor Yellow
        exit 0
    }
}

Write-Host "🚀 Starting deployment..." -ForegroundColor Cyan
Write-Host ""

# Test SSH connection
Write-Host "🔗 Testing SSH connection..." -ForegroundColor Yellow
$sshTest = ssh -i "$SSHKeyPath" -o ConnectTimeout=10 -o BatchMode=yes "$RemoteUser@$RemoteHost" "echo 'SSH connection successful'"
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ SSH connection failed. Please check your SSH configuration." -ForegroundColor Red
    exit 1
}
Write-Host "✅ SSH connection successful!" -ForegroundColor Green
Write-Host ""

# Create backup of current files
Write-Host "💾 Creating backup of current files..." -ForegroundColor Yellow
$timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
$backupCmd = "sudo mkdir -p /opt/auth-service-ui/backups/$timestamp && sudo cp -r $RemoteUIPath/* /opt/auth-service-ui/backups/$timestamp/"
ssh -i "$SSHKeyPath" "$RemoteUser@$RemoteHost" $backupCmd
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Backup created: /opt/auth-service-ui/backups/$timestamp" -ForegroundColor Green
} else {
    Write-Host "⚠️  Backup creation failed, but continuing..." -ForegroundColor Yellow
}
Write-Host ""

# Deploy each file
Write-Host "📁 Deploying files..." -ForegroundColor Yellow
$deploymentSuccess = $true

foreach ($file in $FilesToDeploy) {
    Write-Host "   📄 Deploying $file..." -ForegroundColor Cyan
    
    $localFile = Join-Path $LocalUIPath $file
    $tempRemoteFile = "/tmp/auth-ui-$file"
    $finalRemoteFile = "$RemoteUIPath/$file"
    
    # Copy file to temp location
    scp -i "$SSHKeyPath" "$localFile" "$RemoteUser@$RemoteHost:$tempRemoteFile"
    if ($LASTEXITCODE -ne 0) {
        Write-Host "   ❌ Failed to copy $file to temp location" -ForegroundColor Red
        $deploymentSuccess = $false
        continue
    }
    
    # Move to final location with proper permissions
    $moveCmd = "sudo mv `"$tempRemoteFile`" `"$finalRemoteFile`" && sudo chown www-data:www-data `"$finalRemoteFile`" && sudo chmod 644 `"$finalRemoteFile`""
    ssh -i "$SSHKeyPath" "$RemoteUser@$RemoteHost" $moveCmd
    if ($LASTEXITCODE -eq 0) {
        Write-Host "   ✅ $file deployed successfully" -ForegroundColor Green
    } else {
        Write-Host "   ❌ Failed to deploy $file" -ForegroundColor Red
        $deploymentSuccess = $false
    }
}

Write-Host ""

# Restart nginx to ensure changes take effect
Write-Host "🔄 Restarting nginx..." -ForegroundColor Yellow
ssh -i "$SSHKeyPath" "$RemoteUser@$RemoteHost" "sudo systemctl reload nginx"
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Nginx reloaded successfully!" -ForegroundColor Green
} else {
    Write-Host "⚠️  Nginx reload failed, but files are deployed" -ForegroundColor Yellow
}

Write-Host ""

# Final status
if ($deploymentSuccess) {
    Write-Host "🎉 Deployment completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 Next steps:" -ForegroundColor Cyan
    Write-Host "   1. Test the updated login page at: https://$RemoteHost"
    Write-Host "   2. Verify the professional icons are displaying correctly"
    Write-Host "   3. Test login functionality with the new design"
    Write-Host ""
    Write-Host "🔗 Access the updated UI at: https://$RemoteHost" -ForegroundColor Yellow
} else {
    Write-Host "❌ Deployment completed with errors!" -ForegroundColor Red
    Write-Host "   Please check the error messages above and retry failed deployments."
}

Write-Host ""
Write-Host "📊 Deployment Summary:" -ForegroundColor Cyan
Write-Host "   Environment: $Environment"
Write-Host "   Files Deployed: $($FilesToDeploy.Count)"
Write-Host "   Backup Location: /opt/auth-service-ui/backups/$timestamp"
$statusText = if ($deploymentSuccess) { "SUCCESS" } else { "PARTIAL FAILURE" }
Write-Host "   Status: $statusText"
