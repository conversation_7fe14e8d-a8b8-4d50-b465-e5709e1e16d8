﻿#include <iostream>
#include <memory>
#include <filesystem>
#include "auth_service.hpp"
#include "config_manager.hpp"
#include "command_line_args.hpp"

using namespace AuthService;

/**
 * @brief Print startup banner with version information
 */
void printStartupBanner(const CommandLineArgs& args) {
    if (!args.verbose && !args.debug_mode) return;

    std::cout << "========================================\n";
    std::cout << "  Auth Service - OAuth 2.0 Server\n";
    std::cout << "  Version: 1.0.0\n";
    std::cout << "  Build: " << __DATE__ << " " << __TIME__ << "\n";
    std::cout << "========================================\n";
}

/**
 * @brief Apply command line overrides to configuration
 */
void applyCommandLineOverrides(ConfigManager& config, const CommandLineArgs& args) {
    // Database overrides
    if (args.db_host) {
        std::cout << "Overriding database host: " << *args.db_host << std::endl;
        // Note: Would need ConfigManager methods to support runtime overrides
    }
    if (args.db_port) {
        std::cout << "Overriding database port: " << *args.db_port << std::endl;
    }
    if (args.db_name) {
        std::cout << "Overriding database name: " << *args.db_name << std::endl;
    }
    if (args.db_user) {
        std::cout << "Overriding database user: " << *args.db_user << std::endl;
    }

    // Log level override
    if (args.verbose || args.debug_mode) {
        std::cout << "Log level: " << CommandLineParser::logLevelToString(args.log_level) << std::endl;
    }
}

int main(int argc, char* argv[]) {
    try {
        // Parse command line arguments
        CommandLineParser parser;
        CommandLineArgs args = parser.parse(argc, argv);

        // Handle immediate exit options
        if (args.show_help) {
            std::cout << parser.getHelpText() << std::endl;
            return 0;
        }

        if (args.show_version) {
            std::cout << parser.getVersionInfo() << std::endl;
            return 0;
        }

        // Print startup information
        printStartupBanner(args);

        // Test configuration if requested
        if (args.test_config) {
            std::cout << "Testing configuration file: " << args.config_file << std::endl;
            try {
                auto config_manager = std::make_unique<ConfigManager>(args.config_file);
                std::cout << "✅ Configuration file is valid" << std::endl;
                return 0;
            } catch (const std::exception& e) {
                std::cerr << "❌ Configuration test failed: " << e.what() << std::endl;
                return 1;
            }
        }

        // Create configuration manager
        if (args.verbose) {
            std::cout << "Loading configuration from: " << args.config_file << std::endl;
        }
        auto config_manager = std::make_unique<ConfigManager>(args.config_file);

        // Apply command line overrides
        applyCommandLineOverrides(*config_manager, args);

        // Dry run mode - validate and exit
        if (args.dry_run) {
            std::cout << "✅ Dry run completed successfully" << std::endl;
            std::cout << "Configuration: " << args.config_file << std::endl;
            std::cout << "Port: " << args.port << std::endl;
            std::cout << "Log level: " << CommandLineParser::logLevelToString(args.log_level) << std::endl;
            return 0;
        }

        // Create and start auth service
        auto auth_service = std::make_unique<AuthService>(std::move(config_manager));

        if (args.verbose) {
            std::cout << "Starting Auth Service on port " << args.port << std::endl;
            if (args.daemon_mode) {
                std::cout << "Running in daemon mode" << std::endl;
            }
        }

        auth_service->start(args.port);

        return 0;
    }
    catch (const CommandLineException& e) {
        std::cerr << "Command line error: " << e.what() << std::endl;
        std::cerr << "Use --help for usage information." << std::endl;
        return 1;
    }
    catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}
