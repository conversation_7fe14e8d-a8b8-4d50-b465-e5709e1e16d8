# Auth Service - Command Line Arguments

**Version**: 1.0.0  
**Updated**: 2025-07-17  
**Module**: `command_line_args.hpp` / `command_line_args.cpp`

## 📋 **Overview**

The auth-service application provides comprehensive command line argument support for configuration, debugging, and operational control. All arguments are handled through a dedicated module that separates argument parsing from the main application logic.

## 🖥️ **Available Arguments**

### **Basic Application Control**

| Argument | Short | Type | Default | Description |
|----------|-------|------|---------|-------------|
| `--help` | `-h` | Flag | - | Show help message and exit |
| `--version` | `-v` | Flag | - | Show version information and exit |
| `--dry-run` | - | Flag | - | Validate configuration and exit without starting |
| `--test-config` | - | Flag | - | Test configuration file validity and exit |
| `--daemon` | `-d` | Flag | - | Run as background daemon |

### **Configuration and Runtime Options**

| Argument | Short | Type | Default | Description |
|----------|-------|------|---------|-------------|
| `--config` | `-c` | String | `/etc/auth-service/auth-service.conf` | Configuration file path |
| `--port` | `-p` | Integer | `8082` | Server port (overrides config file) |
| `--log-level` | `-l` | String | `info` | Log level: debug, info, warn, error |
| `--log-file` | - | String | - | Log file path (default: stdout) |
| `--pid-file` | - | String | - | PID file path for daemon mode |

### **Database Override Options**

| Argument | Short | Type | Default | Description |
|----------|-------|------|---------|-------------|
| `--db-host` | - | String | - | Database host (overrides config file) |
| `--db-port` | - | Integer | - | Database port (overrides config file) |
| `--db-name` | - | String | - | Database name (overrides config file) |
| `--db-user` | - | String | - | Database user (overrides config file) |

### **Security and Performance Options**

| Argument | Short | Type | Default | Description |
|----------|-------|------|---------|-------------|
| `--max-connections` | - | Integer | - | Maximum database connections |
| `--worker-threads` | - | Integer | - | Number of worker threads |
| `--no-cors` | - | Flag | - | Disable CORS support |
| `--cors-origin` | - | String | - | Allowed CORS origin (default: *) |

### **Development and Debugging Options**

| Argument | Short | Type | Default | Description |
|----------|-------|------|---------|-------------|
| `--verbose` | - | Flag | - | Enable verbose output |
| `--debug` | - | Flag | - | Enable debug mode |
| `--no-metrics` | - | Flag | - | Disable metrics collection |
| `--metrics-port` | - | Integer | - | Metrics server port (default: disabled) |

## 📚 **Usage Examples**

### **Basic Usage**
```bash
# Show help
./auth-service --help

# Show version information
./auth-service --version

# Start with default settings
./auth-service

# Use custom configuration file
./auth-service --config /path/to/custom/auth-service.conf

# Run on custom port
./auth-service --port 9090
```

### **Configuration Testing**
```bash
# Test configuration file validity
./auth-service --test-config --config /path/to/auth-service.conf

# Dry run - validate everything without starting
./auth-service --dry-run --config /path/to/auth-service.conf --port 8082
```

### **Database Overrides**
```bash
# Override database connection settings
./auth-service \
  --db-host custom-db-server.com \
  --db-port 5433 \
  --db-name custom_auth_db \
  --db-user custom_user
```

### **Production Deployment**
```bash
# Run as daemon with logging
./auth-service \
  --daemon \
  --config /opt/auth-service/config/auth-service.conf \
  --log-file /var/log/auth-service.log \
  --pid-file /var/run/auth-service.pid

# High-performance configuration
./auth-service \
  --config /opt/auth-service/config/auth-service.conf \
  --worker-threads 16 \
  --max-connections 200 \
  --metrics-port 9091
```

### **Development and Debugging**
```bash
# Debug mode with verbose output
./auth-service --debug --verbose --log-level debug

# Development with custom CORS
./auth-service \
  --debug \
  --cors-origin "http://localhost:3000" \
  --log-level debug \
  --port 8082
```

## 🔧 **Argument Validation**

The command line parser includes comprehensive validation:

### **Port Validation**
- All ports must be between 1 and 65535
- Applies to `--port`, `--db-port`, and `--metrics-port`

### **File Path Validation**
- Configuration files must exist (unless using `--dry-run` or `--test-config`)
- Log file and PID file directories must exist
- Provides clear error messages for missing files/directories

### **Log Level Validation**
- Valid levels: `debug`, `info`, `warn`, `error`
- Case-insensitive matching
- Supports `warning` as alias for `warn`

### **Thread and Connection Validation**
- Worker threads and max connections must be at least 1
- Provides sensible error messages for invalid values

## 🏗️ **Architecture Benefits**

### **Separation of Concerns**
- Command line parsing is completely separate from main application logic
- Easy to modify arguments without touching `main.cpp`
- Clean interface through `CommandLineArgs` structure

### **Type Safety**
- Enum-based log levels prevent invalid values
- Optional types for override parameters
- Structured argument access

### **Extensibility**
- Easy to add new arguments by modifying only the parser module
- Validation logic is centralized and reusable
- Help text is automatically generated

### **Error Handling**
- Dedicated exception types for different error categories
- Comprehensive validation with detailed error messages
- Graceful handling of invalid arguments

## 🧪 **Testing**

A test program is provided to verify argument parsing:

```bash
# Compile test program
g++ -std=c++23 -I./include -lboost_program_options \
    test_command_line.cpp src/command_line_args.cpp \
    -o test_command_line

# Run tests
./test_command_line
```

## 📖 **Integration with Main Application**

The main application uses the parser like this:

```cpp
#include "command_line_args.hpp"

int main(int argc, char* argv[]) {
    try {
        CommandLineParser parser;
        CommandLineArgs args = parser.parse(argc, argv);
        
        if (args.show_help) {
            std::cout << parser.getHelpText() << std::endl;
            return 0;
        }
        
        if (args.show_version) {
            std::cout << parser.getVersionInfo() << std::endl;
            return 0;
        }
        
        // Use args.config_file, args.port, etc.
        
    } catch (const CommandLineException& e) {
        std::cerr << e.what() << std::endl;
        return 1;
    }
}
```

## 🔄 **Future Enhancements**

Potential future additions:
- Configuration file generation (`--generate-config`)
- Signal handling options (`--reload-signal`)
- Cluster mode options (`--cluster-node`, `--cluster-port`)
- SSL/TLS certificate options (`--cert-file`, `--key-file`)
- Rate limiting overrides (`--rate-limit`)

The modular design makes these additions straightforward without affecting existing code.
