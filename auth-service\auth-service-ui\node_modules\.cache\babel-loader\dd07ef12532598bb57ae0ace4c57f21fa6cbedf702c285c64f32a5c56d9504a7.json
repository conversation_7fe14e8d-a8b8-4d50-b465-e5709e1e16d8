{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"action\", \"className\", \"message\", \"role\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { emphasize } from '@mui/system/colorManipulator';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport Paper from '../Paper';\nimport { getSnackbarContentUtilityClass } from './snackbarContentClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    action: ['action'],\n    message: ['message']\n  };\n  return composeClasses(slots, getSnackbarContentUtilityClass, classes);\n};\nconst SnackbarContentRoot = styled(Paper, {\n  name: 'MuiSnackbarContent',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(({\n  theme\n}) => {\n  const emphasis = theme.palette.mode === 'light' ? 0.8 : 0.98;\n  const backgroundColor = emphasize(theme.palette.background.default, emphasis);\n  return _extends({}, theme.typography.body2, {\n    color: theme.vars ? theme.vars.palette.SnackbarContent.color : theme.palette.getContrastText(backgroundColor),\n    backgroundColor: theme.vars ? theme.vars.palette.SnackbarContent.bg : backgroundColor,\n    display: 'flex',\n    alignItems: 'center',\n    flexWrap: 'wrap',\n    padding: '6px 16px',\n    borderRadius: (theme.vars || theme).shape.borderRadius,\n    flexGrow: 1,\n    [theme.breakpoints.up('sm')]: {\n      flexGrow: 'initial',\n      minWidth: 288\n    }\n  });\n});\nconst SnackbarContentMessage = styled('div', {\n  name: 'MuiSnackbarContent',\n  slot: 'Message',\n  overridesResolver: (props, styles) => styles.message\n})({\n  padding: '8px 0'\n});\nconst SnackbarContentAction = styled('div', {\n  name: 'MuiSnackbarContent',\n  slot: 'Action',\n  overridesResolver: (props, styles) => styles.action\n})({\n  display: 'flex',\n  alignItems: 'center',\n  marginLeft: 'auto',\n  paddingLeft: 16,\n  marginRight: -8\n});\nconst SnackbarContent = /*#__PURE__*/React.forwardRef(function SnackbarContent(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiSnackbarContent'\n  });\n  const {\n      action,\n      className,\n      message,\n      role = 'alert'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsxs(SnackbarContentRoot, _extends({\n    role: role,\n    square: true,\n    elevation: 6,\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref\n  }, other, {\n    children: [/*#__PURE__*/_jsx(SnackbarContentMessage, {\n      className: classes.message,\n      ownerState: ownerState,\n      children: message\n    }), action ? /*#__PURE__*/_jsx(SnackbarContentAction, {\n      className: classes.action,\n      ownerState: ownerState,\n      children: action\n    }) : null]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? SnackbarContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The action to display. It renders after the message, at the end of the snackbar.\n   */\n  action: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The message to display.\n   */\n  message: PropTypes.node,\n  /**\n   * The ARIA role attribute of the element.\n   * @default 'alert'\n   */\n  role: PropTypes /* @typescript-to-proptypes-ignore */.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default SnackbarContent;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}