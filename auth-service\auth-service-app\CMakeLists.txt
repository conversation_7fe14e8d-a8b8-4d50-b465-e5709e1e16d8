﻿cmake_minimum_required(VERSION 3.20)
project(auth-service VERSION 1.0.0 LANGUAGES CXX)

# C++23 Standard Configuration
set(CMAKE_CXX_STANDARD 23)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Compiler-specific flags for better optimization and debugging
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g -O0 -Wall -Wextra")
elseif(CMAKE_BUILD_TYPE STREQUAL "Release")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -O3 -DNDEBUG")
endif()

# Find required packages
find_package(PkgConfig REQUIRED)
find_package(Boost REQUIRED COMPONENTS system program_options)
find_package(OpenSSL REQUIRED)
pkg_check_modules(PQXX REQUIRED libpqxx)
find_package(n<PERSON>hmann_j<PERSON> REQUIRED)

# Find cpp-httplib for HTTP server functionality
find_path(HTT<PERSON>IB_INCLUDE_DIR httplib.h PATHS /usr/include /usr/local/include)
if(NOT HTTPLIB_INCLUDE_DIR)
    message(STATUS "cpp-httplib not found, downloading header-only library...")
    file(DOWNLOAD
        "https://raw.githubusercontent.com/yhirose/cpp-httplib/master/httplib.h"
        "${CMAKE_CURRENT_SOURCE_DIR}/include/httplib.h"
        SHOW_PROGRESS
    )
    set(HTTPLIB_INCLUDE_DIR "${CMAKE_CURRENT_SOURCE_DIR}/include")
endif()

# Find Argon2 library (Step 3: Password Security)
find_library(ARGON2_LIBRARY NAMES argon2 REQUIRED)
find_path(ARGON2_INCLUDE_DIR argon2.h REQUIRED)

# Verify critical libraries are found
if(NOT ARGON2_LIBRARY)
    message(FATAL_ERROR "Argon2 library not found. Please install libargon2-dev")
endif()

if(NOT OpenSSL_FOUND)
    message(FATAL_ERROR "OpenSSL not found. Please install libssl-dev")
endif()

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)

# Source files for OAuth 2.0 Auth Service with Enhanced RBAC
set(SOURCES
    src/main.cpp
    src/command_line_args.cpp     # Command line argument parsing and management
    src/auth_service.cpp
    src/database_manager.cpp
    src/security_manager.cpp      # Step 3: Argon2id password hashing
    src/config_manager.cpp        # Step 2: OAuth 2.0 configuration
    src/jwt_manager.cpp           # Step 4: JWT token management
    src/http_server.cpp
    src/user_manager.cpp
    src/rate_limiter.cpp          # Rate limiting and security
    src/rbac_manager.cpp          # Enhanced RBAC: Organizations, Projects, Roles, Permissions
    src/enhanced_token_manager.cpp # Enhanced Token Management with Project Scoping
)

# Create the auth-service executable
add_executable(auth-service ${SOURCES})

# Link required libraries
target_link_libraries(auth-service
    PRIVATE
    ${Boost_LIBRARIES}
    pqxx                          # PostgreSQL C++ library
    pq                            # PostgreSQL C library
    OpenSSL::SSL                   # Required for JWT tokens (Step 4)
    OpenSSL::Crypto               # Required for JWT tokens (Step 4)
    nlohmann_json::nlohmann_json  # JSON configuration parsing
    ${ARGON2_LIBRARY}             # Step 3: Argon2id password hashing
    pthread                       # Multi-threading support
)

# Include directories for dependencies
target_include_directories(auth-service
    PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${Boost_INCLUDE_DIRS}
    ${PQXX_INCLUDE_DIRS}
    ${ARGON2_INCLUDE_DIR}
)

# Compiler definitions for OAuth 2.0 + Enhanced RBAC features
target_compile_definitions(auth-service
    PRIVATE
    OAUTH2_ENABLED=1
    ARGON2_ENABLED=1
    RBAC_ENABLED=1
    ENHANCED_TOKENS_ENABLED=1
    $<$<CONFIG:Debug>:DEBUG_BUILD=1>
    $<$<CONFIG:Release>:RELEASE_BUILD=1>
)

# Installation configuration - /opt/auth-service directory structure
install(TARGETS auth-service RUNTIME DESTINATION /opt/auth-service/bin)

# Install configuration files
install(FILES
    config/auth-service.conf
    config/auth-service-enhanced.conf
    DESTINATION /opt/auth-service/config
)

# Install database schema and scripts
install(FILES
    database/auth_schema.sql
    database/enhanced_auth_schema.sql
    database/update_database.sh
    database/update_password.sql
    DESTINATION /opt/auth-service/database
)

# Install development tools
install(FILES
    tools/hash-generator.cpp
    tools/build-hash-generator.sh
    DESTINATION /opt/auth-service/tools
)

# Install testing scripts
install(FILES
    tests/test_validation.sh
    DESTINATION /opt/auth-service/tests
)

# Create logs directory during installation
install(DIRECTORY DESTINATION /opt/auth-service/logs)

# Print build configuration summary
message(STATUS "=== Auth Service Build Configuration ===")
message(STATUS "Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "Boost Version: ${Boost_VERSION}")
message(STATUS "OpenSSL Version: ${OPENSSL_VERSION}")
message(STATUS "PostgreSQL (libpqxx): ${PQXX_VERSION}")
message(STATUS "nlohmann_json: Found")
message(STATUS "Argon2 Library: ${ARGON2_LIBRARY}")
message(STATUS "=== Installation Paths ===")
message(STATUS "Binary: /opt/auth-service/bin/auth-service")
message(STATUS "Config: /opt/auth-service/config/")
message(STATUS "Database: /opt/auth-service/database/")
message(STATUS "Logs: /opt/auth-service/logs/")
message(STATUS "=== OAuth 2.0 + Enhanced RBAC Features ===")
message(STATUS "✅ Step 1: Database Schema - Enhanced Multi-tenant RBAC")
message(STATUS "✅ Step 2: Configuration System - Enhanced RBAC Configuration")
message(STATUS "✅ Step 3: Argon2id Password Security - Ready")
message(STATUS "✅ Step 4: JWT Token Management - Enhanced with Project Scoping")
message(STATUS "✅ Step 5: RBAC Manager - Organizations, Projects, Roles, Permissions")
message(STATUS "✅ Step 6: Enhanced Token Manager - Project-scoped Tokens")
message(STATUS "🎯 Next: API Integration and Testing")
message(STATUS "==========================================")

# Enable testing if requested
option(BUILD_TESTS "Build test programs" OFF)
if(BUILD_TESTS)
    enable_testing()
    message(STATUS "Testing enabled")
endif()
