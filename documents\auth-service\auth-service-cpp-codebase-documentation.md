# Authentication Service C++23 Codebase Documentation

**Update (July 2025):**
- Certificate sync logic is now simplified: only the wildcard certificate (`*.chcit.org`) is used.
- PowerShell SSH execution is robust (not using Invoke-Expression).
- All documentation is current as of July 2025.
- Any references to per-domain certificate logic have been removed as obsolete.

*Last Updated: July 12, 2025*  
*Current Status: Phase 3 - Core Functionality Implemented*

## 📋 **Overview**

This document provides an in-depth, up-to-date analysis of every file in the Authentication Service C++23 application, including their purpose, key functionality, relationships, and implementation status. It reflects the current, fully implemented codebase.

**Base Path**: `D:\Coding_Projects\auth-service\auth-service-app\`  
**Language**: C++23  
**Compiler**: GCC 14.2  
**Target Platform**: Ubuntu 24.04  

---

## 🏗️ Build System

### CMakeLists.txt
- **Path**: `CMakeLists.txt`
- **Purpose**: Defines the build configuration, dependencies, and installation targets for the project.
- **Key Features**:
  - Requires CMake 3.20+, targets C++23
  - Finds and links <PERSON>ost, OpenSSL, PostgreSQL (libpqxx), nlohmann/json, pthread, Argon2
  - Includes all source files from `src/` and headers from `include/`
  - Installs binaries, configuration, and schema to `/opt/auth-service/`
  - Provides build status output and supports test builds
- **Relationships**:
  - All source and header files are referenced here
  - Foundation for dependency management and build reproducibility

---

## 📁 Configuration Files

### config/auth-service.conf & config/auth-service-oauth2.conf
- **Purpose**: JSON configuration files for database, server, OAuth2, JWT, Argon2, and session settings.
- **Key Fields**:
  - `database`: host, port, name, user, password
  - `server`: port, log_level
  - `oauth2.jwt`: secret, access_token_expiry, refresh_token_expiry, algorithm, issuer, audience
  - `oauth2.argon2`: memory_cost, time_cost, parallelism, salt_length
  - `oauth2.session`: timeout, cleanup_interval, max_sessions_per_user
- **Relationships**:
  - Loaded by `ConfigManager` for all runtime configuration
  - Referenced in `main.cpp` and throughout service initialization

### database/auth_schema.sql
- **Purpose**: Defines the PostgreSQL schema for users, tokens, sessions, JWT tokens, and blacklists, with triggers and functions for cleanup and security.
- **Relationships**:
  - Used by `DatabaseManager` for schema creation and migrations
  - Directly maps to C++ data models in `database_manager.hpp`

---

## 📂 Header Files (`include/`)

### auth_service.hpp
- **Purpose**: Main orchestrator class. Owns and coordinates all service components.
- **Key Members**:
  - `config_manager_`, `database_manager_`, `security_manager_`, `jwt_manager_`, `http_server_`, `user_manager_` (all RAII-managed)
- **Relationships**:
  - Created in `main.cpp`, manages lifecycle of all managers and the HTTP server

### config_manager.hpp
- **Purpose**: Loads and provides access to configuration values from JSON files.
- **Key Methods**: Accessors for all configuration fields (DB, server, JWT, Argon2, session)
- **Relationships**:
  - Used by all other managers for settings
  - Constructed in `main.cpp`, passed to all components

### database_manager.hpp
- **Purpose**: Handles all database operations, including user CRUD, token storage, blacklist management, and session handling.
- **Key Features**:
  - Uses libpqxx for PostgreSQL
  - Provides data models for users and tokens
- **Relationships**:
  - Used by `UserManager`, `JWTManager`, and indirectly by `SecurityManager`
  - Receives configuration from `ConfigManager`

### http_server.hpp
- **Purpose**: Implements the OAuth 2.0 REST API endpoints and HTTP server logic.
- **Key Endpoints**:
  - `/oauth/token`, `/oauth/refresh`, `/oauth/validate`, `/oauth/revoke`, `/health`
- **Relationships**:
  - Uses `UserManager` and `JWTManager` for authentication and token operations
  - Owned by `AuthService`

### jwt_manager.hpp
- **Purpose**: Manages JWT token generation, validation, refresh, and revocation. Handles cryptographic operations using OpenSSL.
- **Key Features**:
  - Token creation and parsing
  - Signature verification
  - Integration with DB for token storage and blacklist
- **Relationships**:
  - Uses `ConfigManager` and `DatabaseManager`
  - Used by `HttpServer` and `AuthService`

### security_manager.hpp
- **Purpose**: Provides password hashing (Argon2id), salt generation, and password verification. Also includes token helpers.
- **Key Methods**:
  - `hash_password`, `hash_password_with_salt`, `verify_password`, `generate_salt`
- **Relationships**:
  - Used by `UserManager` for secure password storage and verification
  - Receives configuration from `ConfigManager`

### user_manager.hpp
- **Purpose**: Implements user registration, authentication, and profile management, integrating with DB and security layers.
- **Key Methods**:
  - `create_user`, `authenticate_user`, `delete_user`, `update_last_login`, validation helpers
- **Relationships**:
  - Uses `DatabaseManager` and `SecurityManager`
  - Used by `HttpServer` for user-related API endpoints

---

## 📦 Source Files (`src/`)

### main.cpp
- **Purpose**: Application entry point. Parses command-line options, initializes configuration, and starts the service.
- **Relationships**:
  - Instantiates `ConfigManager` and `AuthService`
  - Passes configuration and port to service

### auth_service.cpp
- **Purpose**: Implements the `AuthService` class, initializing all major components and starting/stopping the HTTP server.
- **Relationships**:
  - Owns and manages all service managers
  - Calls `database_manager_->initialize()` and `jwt_manager_->initialize()`

### config_manager.cpp
- **Purpose**: Implements configuration loading from file or defaults. Provides getters for all config parameters.
- **Relationships**:
  - Used by all managers for runtime configuration

### database_manager.cpp
- **Purpose**: Implements all database logic, including user CRUD, token storage, blacklist checks, and cleanup.
- **Key Features**:
  - Connects to PostgreSQL using libpqxx
  - Provides utility methods for converting DB rows to C++ structs
- **Relationships**:
  - Used by `UserManager` and `JWTManager`

### http_server.cpp
- **Purpose**: Implements the HTTP server and all OAuth2 API endpoints. Handles routing, request parsing, and response generation.
- **Key Endpoints**: `/oauth/token`, `/oauth/refresh`, `/oauth/validate`, `/oauth/revoke`, `/health`
- **Relationships**:
  - Uses `UserManager` for user operations and `JWTManager` for token management

### jwt_manager.cpp
- **Purpose**: Implements JWT creation, validation, refresh, revocation, and storage. Handles base64 encoding/decoding and cryptographic signing with OpenSSL.
- **Relationships**:
  - Uses `ConfigManager` for settings and `DatabaseManager` for persistence
  - Used by `HttpServer` for all token-related API logic

### security_manager.cpp
- **Purpose**: Implements Argon2id password hashing, salt generation, and password verification. Provides helpers for encoding/decoding hashes and salts.
- **Relationships**:
  - Used by `UserManager` for secure password storage

### user_manager.cpp
- **Purpose**: Implements user registration, authentication, and deletion. Validates inputs, hashes passwords, and interacts with DB and security layers.
- **Relationships**:
  - Uses `DatabaseManager` and `SecurityManager`
  - Used by `HttpServer` for user authentication and registration endpoints
Manages configuration loading and provides access to configuration values.

#### **Class Definition**:
```cpp
class ConfigManager {
public:
    explicit ConfigManager(const std::string& config_file);
    void load_config(const std::string& config_file);
    std::string get_database_host() const;
    int get_database_port() const;
    std::string get_database_name() const;
    std::string get_database_user() const;
    std::string get_database_password() const;
    int get_server_port() const;
    std::string get_log_level() const;
private:
    nlohmann::json config_;
};
```

#### **Dependencies**:
- **External**: `nlohmann/json` for JSON parsing
- **Standard**: `<string>`, `<fstream>`
- **Relationships**: Used by all other components for configuration

#### **Current Status**: ✅ **Interface Complete** - Ready for implementation

### **database_manager.hpp** ✅ **COMPLETE INTERFACE**
- **Path**: `include/database_manager.hpp`
- **Size**: 333 bytes
- **Last Modified**: June 25, 2025 8:51:08 AM
- **Status**: ✅ **Interface Complete**

#### **Purpose**:
Manages PostgreSQL database connections and operations.

#### **Class Definition**:
```cpp
class DatabaseManager {
public:
    explicit DatabaseManager(ConfigManager* config);
    ~DatabaseManager();
    void initialize();
    bool test_connection();
private:
    ConfigManager* config_;
};
```

#### **Dependencies**:
- **Internal**: ConfigManager for database configuration
- **External**: libpqxx (planned for real implementation)
- **Relationships**: Used by UserManager for data persistence

#### **Current Status**: ✅ **Interface Complete** - Stub implementation, needs real PostgreSQL integration

### **http_server.hpp** ✅ **COMPLETE INTERFACE**
- **Path**: `include/http_server.hpp`
- **Size**: 270 bytes
- **Last Modified**: June 25, 2025 8:52:09 AM
- **Status**: ✅ **Interface Complete**

#### **Purpose**:
Provides HTTP REST API endpoints for authentication services.

#### **Class Definition**:
```cpp
class HttpServer {
public:
    explicit HttpServer(UserManager* user_manager);
    ~HttpServer();
    void start(int port);
    void stop();
private:
    UserManager* user_manager_;
    bool running_;
};
```

#### **Dependencies**:
- **Internal**: UserManager for authentication operations
- **External**: HTTP framework (planned - Crow, Beast, or similar)
- **Relationships**: Exposes UserManager functionality via REST API

#### **Current Status**: ✅ **Interface Complete** - Stub implementation, needs real HTTP server

### **security_manager.hpp** ✅ **COMPLETE INTERFACE**
- **Path**: `include/security_manager.hpp`
- **Size**: 372 bytes
- **Last Modified**: June 25, 2025 8:51:44 AM
- **Status**: ✅ **Interface Complete**

#### **Purpose**:
Handles password hashing, JWT token generation, and cryptographic operations.

#### **Class Definition**:
```cpp
class SecurityManager {
public:
    SecurityManager();
    ~SecurityManager();
    std::string hash_password(const std::string& password) const;
    bool verify_password(const std::string& password, const std::string& hash) const;
    std::string generate_token() const;
    bool validate_token(const std::string& token) const;
private:
    // Implementation details
};
```

#### **Dependencies**:
- **External**: Argon2id library (planned for password hashing)
- **External**: JWT library (planned for token management)
- **External**: OpenSSL for cryptographic operations
- **Relationships**: Used by UserManager for security operations

#### **Current Status**: ✅ **Interface Complete** - Stub implementation, needs real cryptography

### **user_manager.hpp** ✅ **COMPLETE INTERFACE**
- **Path**: `include/user_manager.hpp`
- **Size**: 515 bytes
- **Last Modified**: June 25, 2025 8:52:45 AM
- **Status**: ✅ **Interface Complete**

#### **Purpose**:
Manages user operations including creation, authentication, and management.

#### **Class Definition**:
```cpp
class UserManager {
public:
    UserManager(DatabaseManager* db_manager, SecurityManager* sec_manager);
    ~UserManager();
    bool create_user(const std::string& username, const std::string& password);
    bool authenticate_user(const std::string& username, const std::string& password);
    bool delete_user(const std::string& username);
    bool update_user(const std::string& username, const std::string& new_password);
private:
    DatabaseManager* db_manager_;
    SecurityManager* sec_manager_;
};
```

#### **Dependencies**:
- **Internal**: DatabaseManager for data persistence
- **Internal**: SecurityManager for password operations
- **Relationships**: Central business logic component used by HttpServer

#### **Current Status**: ✅ **Interface Complete** - Stub implementation, needs real user operations

-----------------------------------------------------------------------------------------------------------

## 📂 **Source Files (src/)**

### **main.cpp** ✅ **FUNCTIONAL**
- **Path**: `src/main.cpp`
- **Size**: 1,283 bytes
- **Last Modified**: June 25, 2025 7:22:21 AM
- **Status**: ✅ **Functional Entry Point**

#### **Purpose**:
Application entry point with command-line argument parsing and service initialization.

#### **Key Functionality**:
```cpp
int main(int argc, char* argv[]) {
    // Command-line options using Boost.Program_options
    po::options_description desc("Auth Service Options");
    desc.add_options()
        ("help,h", "Show help message")
        ("config,c", po::value<std::string>()->default_value("/etc/auth-service/auth-service.conf"), "Configuration file path")
        ("port,p", po::value<int>()->default_value(8082), "Server port");
    
    // Service initialization and startup
    auto config = std::make_unique<ConfigManager>(config_file);
    AuthService service(std::move(config));
    service.start(port);
}
```

#### **Dependencies**:
- **External**: Boost.Program_options for command-line parsing
- **Internal**: ConfigManager, AuthService
- **Standard**: `<iostream>`, `<memory>`

#### **Current Status**: ✅ **Functional** - Properly initializes and starts service

### **auth_service.cpp** ✅ **FUNCTIONAL SKELETON**
- **Path**: `src/auth_service.cpp`
- **Size**: 975 bytes
- **Last Modified**: June 25, 2025 7:51:40 AM
- **Status**: ✅ **Skeleton Implementation**

#### **Purpose**:
Main service implementation that coordinates all components.

#### **Key Implementation**:
```cpp
AuthService::AuthService(std::unique_ptr<ConfigManager> config)
    : config_manager_(std::move(config)) {
    database_manager_ = std::make_unique<DatabaseManager>(config_manager_.get());
    security_manager_ = std::make_unique<SecurityManager>();
    user_manager_ = std::make_unique<UserManager>(database_manager_.get(), security_manager_.get());
    http_server_ = std::make_unique<HttpServer>(user_manager_.get());
}

void AuthService::start(int port) {
    std::cout << "Auth Service starting..." << std::endl;
    database_manager_->initialize();
    http_server_->start(port);
    // Service loop implementation
}
```

#### **Dependencies**:
- **Internal**: All manager classes (Database, Security, User, Http)
- **Relationships**: Orchestrates component lifecycle and dependencies

#### **Current Status**: ✅ **Skeleton Complete** - Proper dependency injection, needs real implementation

### **config_manager.cpp** ✅ **FUNCTIONAL**
- **Path**: `src/config_manager.cpp`
- **Size**: 1,474 bytes
- **Last Modified**: June 25, 2025 7:53:35 AM
- **Status**: ✅ **Functional Implementation**

#### **Purpose**:
Loads and manages configuration from JSON files with fallback defaults.

#### **Key Implementation**:
```cpp
ConfigManager::ConfigManager(const std::string& config_file) {
    load_config(config_file);
}

void ConfigManager::load_config(const std::string& config_file) {
    std::ifstream file(config_file);
    if (!file.is_open()) {
        std::cerr << "Warning: Could not open config file: " << config_file << ". Using defaults." << std::endl;
        // Fallback to default configuration
        config_ = nlohmann::json{
            {"database", {
                {"host", "***********"},
                {"port", 5432},
                {"name", "auth_service"},
                {"user", "auth_service"},
                {"password", "password2311"}
            }},
            {"server", {
                {"port", 8082},
                {"log_level", "info"}
            }}
        };
        return;
    }
    file >> config_;
}
```

#### **Dependencies**:
- **External**: nlohmann/json for JSON parsing
- **Standard**: `<fstream>`, `<iostream>`

#### **Current Status**: ✅ **Fully Functional** - Handles file loading, defaults, and error cases

### **database_manager.cpp** ⚠️ **STUB IMPLEMENTATION**
- **Path**: `src/database_manager.cpp`
- **Size**: 513 bytes
- **Last Modified**: June 24, 2025 9:55:01 PM
- **Status**: ⚠️ **Stub Only - Needs Real Implementation**

#### **Purpose**:
Database connection and operation management (currently stubbed).

#### **Current Implementation**:
```cpp
DatabaseManager::DatabaseManager(ConfigManager* config) : config_(config) {}

void DatabaseManager::initialize() {
    std::cout << "Initializing database connection..." << std::endl;
    std::cout << "Database connection initialized." << std::endl;
}

bool DatabaseManager::test_connection() {
    std::cout << "Testing database connection..." << std::endl;
    return true;  // FAKE SUCCESS
}
```

#### **Dependencies**:
- **Internal**: ConfigManager for database configuration
- **Missing**: libpqxx for real PostgreSQL connectivity

#### **Current Status**: ⚠️ **STUB ONLY** - Needs complete rewrite with real PostgreSQL integration

### **http_server.cpp** ⚠️ **STUB IMPLEMENTATION**
- **Path**: `src/http_server.cpp`
- **Size**: 963 bytes
- **Last Modified**: June 24, 2025 9:55:01 PM
- **Status**: ⚠️ **Stub Only - Needs Real Implementation**

#### **Purpose**:
HTTP server for REST API endpoints (currently stubbed).

#### **Current Implementation**:
```cpp
HttpServer::HttpServer(UserManager* user_manager) 
    : user_manager_(user_manager), running_(false) {}

void HttpServer::start(int port) {
    std::cout << "Starting HTTP server on port " << port << "..." << std::endl;
    running_ = true;
    std::cout << "HTTP server started. Listening for requests..." << std::endl;
    
    // STUB: Simple simulation loop
    while (running_) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }
}
```

#### **Dependencies**:
- **Internal**: UserManager for authentication operations
- **Missing**: HTTP framework (Crow, Beast, or similar)

#### **Current Status**: ⚠️ **STUB ONLY** - Needs complete rewrite with real HTTP server

### **security_manager.cpp** ⚠️ **STUB IMPLEMENTATION**
- **Path**: `src/security_manager.cpp`
- **Size**: 816 bytes
- **Last Modified**: June 24, 2025 9:55:01 PM
- **Status**: ⚠️ **Stub Only - Needs Real Implementation**

#### **Purpose**:
Security operations including password hashing and JWT tokens (currently stubbed).

#### **Current Implementation**:
```cpp
std::string SecurityManager::hash_password(const std::string& password) const {
    std::cout << "Hashing password..." << std::endl;
    return "hashed_" + password;  // FAKE HASHING!
}

bool SecurityManager::verify_password(const std::string& password, const std::string& hash) const {
    std::cout << "Verifying password..." << std::endl;
    return hash == "hashed_" + password;  // FAKE VERIFICATION!
}

std::string SecurityManager::generate_token() const {
    std::cout << "Generating token..." << std::endl;
    return "token_12345";  // FAKE TOKEN!
}
```

#### **Dependencies**:
- **Missing**: Argon2id library for password hashing
- **Missing**: JWT library for token management
- **Available**: OpenSSL (linked but not used)

#### **Current Status**: ⚠️ **STUB ONLY** - Needs complete rewrite with real cryptography

### **user_manager.cpp** ⚠️ **STUB IMPLEMENTATION**
- **Path**: `src/user_manager.cpp`
- **Size**: 1,407 bytes
- **Last Modified**: June 24, 2025 9:55:01 PM
- **Status**: ⚠️ **Stub Only - Needs Real Implementation**

#### **Purpose**:
User management operations (currently stubbed).

#### **Current Implementation**:
```cpp
bool UserManager::create_user(const std::string& username, const std::string& password) {
    std::cout << "Creating user: " << username << std::endl;
    std::string hashed_password = sec_manager_->hash_password(password);
    std::cout << "User created successfully: " << username << std::endl;
    return true;  // FAKE SUCCESS
}

bool UserManager::authenticate_user(const std::string& username, const std::string& password) {
    std::cout << "Authenticating user: " << username << std::endl;
    // FAKE AUTHENTICATION
    return true;
}
```

#### **Dependencies**:
- **Internal**: DatabaseManager (for data persistence)
- **Internal**: SecurityManager (for password operations)

#### **Current Status**: ⚠️ **STUB ONLY** - Needs real database operations and business logic

-----------------------------------------------------------------------------------------------------------

## 📊 **Implementation Status Summary**

### ✅ **Complete and Functional**
- **CMakeLists.txt**: Build system fully configured
- **main.cpp**: Entry point with proper initialization
- **config_manager.hpp/.cpp**: Configuration management working
- **All header files**: Complete interfaces defined

### ✅ **Skeleton Complete**
- **auth_service.hpp/.cpp**: Service orchestration framework ready
- **Project Structure**: All files present and organized

### ⚠️ **Stub Implementation - Needs Real Implementation**
- **database_manager.cpp**: Needs PostgreSQL integration
- **http_server.cpp**: Needs HTTP framework integration
- **security_manager.cpp**: Needs Argon2id and JWT implementation
- **user_manager.cpp**: Needs real business logic and database operations

### 📋 **Next Implementation Priorities**
1. **Database Integration**: Replace DatabaseManager stubs with libpqxx
2. **Security Implementation**: Add Argon2id and JWT libraries
3. **HTTP Server**: Integrate Crow or similar HTTP framework
4. **User Operations**: Implement real CRUD operations

-----------------------------------------------------------------------------------------------------------

## 🔗 **Component Relationships**

```
main.cpp
    └── AuthService
        ├── ConfigManager ✅ (Functional)
        ├── DatabaseManager ⚠️ (Stub)
        ├── SecurityManager ⚠️ (Stub)
        ├── UserManager ⚠️ (Stub)
        │   ├── DatabaseManager ⚠️
        │   └── SecurityManager ⚠️
        └── HttpServer ⚠️ (Stub)
            └── UserManager ⚠️
```

### **Dependency Flow**:
1. **main.cpp** → Creates ConfigManager → Creates AuthService
2. **AuthService** → Creates all manager components with proper dependency injection
3. **UserManager** → Uses DatabaseManager and SecurityManager
4. **HttpServer** → Uses UserManager for API operations

-----------------------------------------------------------------------------------------------------------

*This codebase is in Phase 2 (Skeleton Complete) and ready for Phase 3 (Core Functionality Implementation). All interfaces are defined and the application compiles and runs with stub implementations.*
