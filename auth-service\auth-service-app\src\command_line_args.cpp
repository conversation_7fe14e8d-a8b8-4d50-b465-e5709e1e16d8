#include "command_line_args.hpp"
#include <iostream>
#include <sstream>
#include <algorithm>
#include <filesystem>

namespace po = boost::program_options;

namespace AuthServiceCLI {

// Version information - could be populated from CMake or build system
const std::string VERSION = "1.0.0";
const std::string BUILD_DATE = __DATE__;
const std::string BUILD_TIME = __TIME__;
const std::string CXX_STANDARD = "C++23";

CommandLineParser::CommandLineParser() : desc_("Auth Service Options") {
    setupOptions();
}

void CommandLineParser::setupOptions() {
    desc_.add_options()
        // Basic application control
        ("help,h", "Show this help message and exit")
        ("version,v", "Show version information and exit")
        ("dry-run", "Validate configuration and exit without starting service")
        ("test-config", "Test configuration file validity and exit")
        ("daemon,d", "Run as background daemon")
        
        // Configuration and runtime options
        ("config,c", po::value<std::string>()->default_value("/opt/auth-service/config/auth-service.conf"),
         "Configuration file path")
        ("port,p", po::value<int>()->default_value(8082),
         "Server port (overrides config file)")
        ("log-level,l", po::value<std::string>()->default_value("info"),
         "Log level: debug, info, warn, error")
        ("log-file", po::value<std::string>(),
         "Log file path (default: stdout)")
        ("pid-file", po::value<std::string>(),
         "PID file path for daemon mode")
        
        // Database override options
        ("db-host", po::value<std::string>(),
         "Database host (overrides config file)")
        ("db-port", po::value<int>(),
         "Database port (overrides config file)")
        ("db-name", po::value<std::string>(),
         "Database name (overrides config file)")
        ("db-user", po::value<std::string>(),
         "Database user (overrides config file)")
        
        // Security and performance options
        ("max-connections", po::value<int>(),
         "Maximum database connections")
        ("worker-threads", po::value<int>(),
         "Number of worker threads")
        ("no-cors", "Disable CORS support")
        ("cors-origin", po::value<std::string>(),
         "Allowed CORS origin (default: *)")
        
        // Development and debugging options
        ("verbose", "Enable verbose output")
        ("debug", "Enable debug mode")
        ("no-metrics", "Disable metrics collection")
        ("metrics-port", po::value<int>(),
         "Metrics server port (default: disabled)");
}

CommandLineArgs CommandLineParser::parse(int argc, char* argv[]) {
    try {
        po::store(po::parse_command_line(argc, argv, desc_), vm_);
        po::notify(vm_);
        
        CommandLineArgs args = extractArgs();
        
        // Validate arguments
        if (!validateArguments(args)) {
            auto errors = getValidationErrors(args);
            throw ArgumentValidationException(errors);
        }
        
        return args;
    }
    catch (const po::error& e) {
        throw CommandLineException("Failed to parse arguments: " + std::string(e.what()));
    }
}

CommandLineArgs CommandLineParser::extractArgs() const {
    CommandLineArgs args;
    
    // Basic application control
    args.show_help = vm_.count("help") > 0;
    args.show_version = vm_.count("version") > 0;
    args.dry_run = vm_.count("dry-run") > 0;
    args.test_config = vm_.count("test-config") > 0;
    args.daemon_mode = vm_.count("daemon") > 0;
    
    // Configuration and runtime options
    args.config_file = vm_["config"].as<std::string>();
    args.port = vm_["port"].as<int>();
    args.log_level = stringToLogLevel(vm_["log-level"].as<std::string>());
    
    if (vm_.count("log-file")) {
        args.log_file = vm_["log-file"].as<std::string>();
    }
    if (vm_.count("pid-file")) {
        args.pid_file = vm_["pid-file"].as<std::string>();
    }
    
    // Database override options
    if (vm_.count("db-host")) {
        args.db_host = vm_["db-host"].as<std::string>();
    }
    if (vm_.count("db-port")) {
        args.db_port = vm_["db-port"].as<int>();
    }
    if (vm_.count("db-name")) {
        args.db_name = vm_["db-name"].as<std::string>();
    }
    if (vm_.count("db-user")) {
        args.db_user = vm_["db-user"].as<std::string>();
    }
    
    // Security and performance options
    if (vm_.count("max-connections")) {
        args.max_connections = vm_["max-connections"].as<int>();
    }
    if (vm_.count("worker-threads")) {
        args.worker_threads = vm_["worker-threads"].as<int>();
    }
    args.enable_cors = vm_.count("no-cors") == 0;
    if (vm_.count("cors-origin")) {
        args.cors_origin = vm_["cors-origin"].as<std::string>();
    }
    
    // Development and debugging options
    args.verbose = vm_.count("verbose") > 0;
    args.debug_mode = vm_.count("debug") > 0;
    args.enable_metrics = vm_.count("no-metrics") == 0;
    if (vm_.count("metrics-port")) {
        args.metrics_port = vm_["metrics-port"].as<int>();
    }
    
    return args;
}

std::string CommandLineParser::getHelpText() const {
    std::ostringstream oss;
    oss << "Auth Service - Enterprise OAuth 2.0 Authentication Server\n\n";
    oss << "Usage: auth-service [OPTIONS]\n\n";
    oss << desc_ << "\n";
    oss << "Examples:\n";
    oss << "  auth-service --help                    Show this help\n";
    oss << "  auth-service --version                 Show version info\n";
    oss << "  auth-service --config /path/to/config  Use custom config\n";
    oss << "  auth-service --port 9090               Run on port 9090\n";
    oss << "  auth-service --daemon --log-file /var/log/auth-service.log\n";
    oss << "                                         Run as daemon with logging\n";
    oss << "  auth-service --dry-run                 Test configuration\n";
    oss << "  auth-service --debug --verbose         Debug mode with verbose output\n\n";
    oss << "For more information, see the documentation at:\n";
    oss << "https://docs.chcit.org/auth-service/\n";
    return oss.str();
}

std::string CommandLineParser::getVersionInfo() const {
    std::ostringstream oss;
    oss << "Auth Service " << VERSION << "\n";
    oss << "Built: " << BUILD_DATE << " " << BUILD_TIME << "\n";
    oss << "C++ Standard: " << CXX_STANDARD << "\n";
    oss << "Compiler: " << __VERSION__ << "\n";
    oss << "Features: OAuth 2.0, RBAC, Argon2id, JWT, Multi-tenant\n";
    oss << "Copyright (c) 2025 CHCIT DevOps Team\n";
    return oss.str();
}

bool CommandLineParser::validateArguments(const CommandLineArgs& args) const {
    auto errors = getValidationErrors(args);
    return errors.empty();
}

std::vector<std::string> CommandLineParser::getValidationErrors(const CommandLineArgs& args) const {
    std::vector<std::string> errors;
    
    // Port validation
    if (args.port < 1 || args.port > 65535) {
        errors.push_back("Port must be between 1 and 65535");
    }
    
    // Database port validation
    if (args.db_port && (*args.db_port < 1 || *args.db_port > 65535)) {
        errors.push_back("Database port must be between 1 and 65535");
    }
    
    // Metrics port validation
    if (args.metrics_port && (*args.metrics_port < 1 || *args.metrics_port > 65535)) {
        errors.push_back("Metrics port must be between 1 and 65535");
    }
    
    // Configuration file validation
    if (!args.config_file.empty() && !args.dry_run && !args.test_config) {
        if (!std::filesystem::exists(args.config_file)) {
            errors.push_back("Configuration file does not exist: " + args.config_file);
        }
    }
    
    // PID file validation for daemon mode
    if (args.daemon_mode && args.pid_file) {
        std::filesystem::path pid_path(*args.pid_file);
        if (!std::filesystem::exists(pid_path.parent_path())) {
            errors.push_back("PID file directory does not exist: " + pid_path.parent_path().string());
        }
    }
    
    // Log file validation
    if (args.log_file) {
        std::filesystem::path log_path(*args.log_file);
        if (!std::filesystem::exists(log_path.parent_path())) {
            errors.push_back("Log file directory does not exist: " + log_path.parent_path().string());
        }
    }
    
    // Thread count validation
    if (args.worker_threads && *args.worker_threads < 1) {
        errors.push_back("Worker threads must be at least 1");
    }
    
    // Connection count validation
    if (args.max_connections && *args.max_connections < 1) {
        errors.push_back("Max connections must be at least 1");
    }
    
    return errors;
}

std::string CommandLineParser::logLevelToString(LogLevel level) {
    switch (level) {
        case LogLevel::DEBUG: return "debug";
        case LogLevel::INFO:  return "info";
        case LogLevel::WARN:  return "warn";
        case LogLevel::ERROR: return "error";
        default: return "info";
    }
}

LogLevel CommandLineParser::stringToLogLevel(const std::string& level_str) {
    std::string lower_str = level_str;
    std::transform(lower_str.begin(), lower_str.end(), lower_str.begin(), ::tolower);
    
    if (lower_str == "debug") return LogLevel::DEBUG;
    if (lower_str == "info")  return LogLevel::INFO;
    if (lower_str == "warn" || lower_str == "warning") return LogLevel::WARN;
    if (lower_str == "error") return LogLevel::ERROR;
    
    throw std::invalid_argument("Invalid log level: " + level_str + 
                               ". Valid levels: debug, info, warn, error");
}

// Exception implementations
ArgumentValidationException::ArgumentValidationException(const std::vector<std::string>& errors)
    : CommandLineException("Argument validation failed"), errors_(errors) {
    std::ostringstream oss;
    oss << "Argument validation failed:\n";
    for (const auto& error : errors) {
        oss << "  - " << error << "\n";
    }
    // Update the message with detailed errors
    static_cast<std::runtime_error&>(*this) = std::runtime_error(oss.str());
}

} // namespace AuthServiceCLI
