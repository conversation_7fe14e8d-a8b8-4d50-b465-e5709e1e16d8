import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Box,
  Alert,
  Link,
  Divider,
  Chip,
} from '@mui/material';
import { Lock, Person } from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { ApiError } from '../types/auth';

const LoginPage: React.FC = () => {
  const [username, setUsername] = useState('testuser');
  const [password, setPassword] = useState('testpass123');
  const [error, setError] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const { login } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      await login({ username, password });
      navigate('/dashboard');
    } catch (err: any) {
      const apiError = err as ApiError;
      setError(apiError.error_description || 'Login failed');
    } finally {
      setIsLoading(false);
    }
  };

  const handleForgotPassword = () => {
    alert('For password reset, please contact your system <NAME_EMAIL>');
  };

  return (
    <Card
      sx={{
        width: '100%',
        maxWidth: 420,
        height: 'auto',
        maxHeight: '90vh',
        overflow: 'hidden',
      }}
    >
      <CardContent sx={{ p: 3 }}>
        {/* Header */}
        <Box textAlign="center" mb={3}>
          <Lock sx={{ fontSize: 48, color: 'primary.main', mb: 1 }} />
          <Typography variant="h4" component="h1" gutterBottom>
            OAuth 2.0 Service
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Secure Authentication Dashboard
          </Typography>
        </Box>

        {/* Error Alert */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Login Form */}
        <Box component="form" onSubmit={handleSubmit}>
          <TextField
            fullWidth
            placeholder="Username *"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            margin="normal"
            required
            InputProps={{
              startAdornment: <Person sx={{ color: 'rgba(0, 0, 0, 0.54)', mr: 1 }} />,
            }}
            onFocus={() => {
              if (username === 'testuser') setUsername('');
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'white',
                borderRadius: 1,
              }
            }}
          />

          <TextField
            fullWidth
            type="password"
            placeholder="Password *"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            margin="normal"
            required
            InputProps={{
              startAdornment: <Lock sx={{ color: 'rgba(0, 0, 0, 0.54)', mr: 1 }} />,
            }}
            onFocus={() => {
              if (password === 'testpass123') setPassword('');
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'white',
                borderRadius: 1,
              }
            }}
          />

          <Button
            type="submit"
            fullWidth
            variant="contained"
            size="large"
            disabled={isLoading}
            sx={{ mt: 3, mb: 2 }}
          >
            {isLoading ? 'Signing In...' : 'Sign In'}
          </Button>

          <Box textAlign="center">
            <Link
              component="button"
              type="button"
              variant="body2"
              onClick={handleForgotPassword}
              sx={{ textDecoration: 'none' }}
            >
              Forgot Password?
            </Link>
          </Box>
        </Box>

        <Divider sx={{ my: 3 }}>
          <Chip label="Test Credentials" size="small" />
        </Divider>

        {/* Test Credentials */}
        <Box
          sx={{
            bgcolor: 'primary.50',
            border: 1,
            borderColor: 'primary.200',
            borderRadius: 1,
            p: 2,
            textAlign: 'center',
          }}
        >
          <Typography variant="body2" color="primary.main" fontWeight="medium">
            Demo Credentials
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Username: <strong>testuser</strong>
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Password: <strong>testpass123</strong>
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

export default LoginPage;
