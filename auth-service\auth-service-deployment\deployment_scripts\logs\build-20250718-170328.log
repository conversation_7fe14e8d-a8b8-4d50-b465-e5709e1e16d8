Running CMake configuration...
-- The CXX compiler identification is GNU 14.2.0
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/g++-14 - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found PkgConfig: /usr/bin/pkg-config (found version "1.8.1") 
-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.83.0/BoostConfig.cmake (found version "1.83.0") found components: system program_options 
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.13")  
-- Checking for module 'libpqxx'
--   Found libpqxx, version 7.8.1
-- Found nlohmann_json: /usr/share/cmake/nlohmann_json/nlohmann_jsonConfig.cmake (found version "3.11.3") 
-- cpp-httplib not found, downloading header-only library...
-- [download 0% complete]
-- [download 1% complete]
-- [download 2% complete]
-- [download 3% complete]
-- [download 4% complete]
-- [download 5% complete]
-- [download 6% complete]
-- [download 7% complete]
-- [download 8% complete]
-- [download 9% complete]
-- [download 10% complete]
-- [download 11% complete]
-- [download 12% complete]
-- [download 13% complete]
-- [download 17% complete]
-- [download 21% complete]
-- [download 25% complete]
-- [download 29% complete]
-- [download 33% complete]
-- [download 38% complete]
-- [download 42% complete]
-- [download 46% complete]
-- [download 50% complete]
-- [download 54% complete]
-- [download 58% complete]
-- [download 63% complete]
-- [download 67% complete]
-- [download 71% complete]
-- [download 75% complete]
-- [download 79% complete]
-- [download 83% complete]
-- [download 88% complete]
-- [download 92% complete]
-- [download 96% complete]
-- [download 100% complete]
-- === Auth Service Build Configuration ===
-- Build Type: Release
-- C++ Standard: 23
-- Boost Version: 1.83.0
-- OpenSSL Version: 3.0.13
-- PostgreSQL (libpqxx): 7.8.1
-- nlohmann_json: Found
-- Argon2 Library: /usr/lib/x86_64-linux-gnu/libargon2.so
-- === Installation Paths ===
-- Binary: /opt/auth-service/bin/auth-service
-- Config: /opt/auth-service/config/
-- Database: /opt/auth-service/database/
-- Logs: /opt/auth-service/logs/
-- === OAuth 2.0 + Enhanced RBAC Features ===
-- ✅ Step 1: Database Schema - Enhanced Multi-tenant RBAC
-- ✅ Step 2: Configuration System - Enhanced RBAC Configuration
-- ✅ Step 3: Argon2id Password Security - Ready
-- ✅ Step 4: JWT Token Management - Enhanced with Project Scoping
-- ✅ Step 5: RBAC Manager - Organizations, Projects, Roles, Permissions
-- ✅ Step 6: Enhanced Token Manager - Project-scoped Tokens
-- 🎯 Next: API Integration and Testing
-- ==========================================
-- Configuring done (0.4s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/auth-service-build/build
Starting compilation...
[  7%] Building CXX object CMakeFiles/auth-service.dir/src/main.cpp.o
[ 15%] Building CXX object CMakeFiles/auth-service.dir/src/command_line_args.cpp.o
[ 23%] Building CXX object CMakeFiles/auth-service.dir/src/auth_service.cpp.o
[ 30%] Building CXX object CMakeFiles/auth-service.dir/src/database_manager.cpp.o
[ 38%] Building CXX object CMakeFiles/auth-service.dir/src/security_manager.cpp.o
[ 46%] Building CXX object CMakeFiles/auth-service.dir/src/config_manager.cpp.o
[ 53%] Building CXX object CMakeFiles/auth-service.dir/src/jwt_manager.cpp.o
[ 61%] Building CXX object CMakeFiles/auth-service.dir/src/http_server.cpp.o
[ 69%] Building CXX object CMakeFiles/auth-service.dir/src/user_manager.cpp.o
[ 76%] Building CXX object CMakeFiles/auth-service.dir/src/rate_limiter.cpp.o
[ 84%] Building CXX object CMakeFiles/auth-service.dir/src/rbac_manager.cpp.o
/home/<USER>/auth-service-build/src/rbac_manager.cpp: In member function ‘std::vector<RBACManager::Project> RBACManager::listProjectsByOrganization(const std::string&, bool)’:
/home/<USER>/auth-service-build/src/rbac_manager.cpp:565:25: error: ‘struct RBACManager::Project’ has no member named ‘organization_id’
  565 |                 project.organization_id = row["organization_id"].as<std::string>();
      |                         ^~~~~~~~~~~~~~~
make[2]: *** [CMakeFiles/auth-service.dir/build.make:216: CMakeFiles/auth-service.dir/src/rbac_manager.cpp.o] Error 1
make[2]: *** Waiting for unfinished jobs....
make[1]: *** [CMakeFiles/Makefile2:83: CMakeFiles/auth-service.dir/all] Error 2
make: *** [Makefile:136: all] Error 2
