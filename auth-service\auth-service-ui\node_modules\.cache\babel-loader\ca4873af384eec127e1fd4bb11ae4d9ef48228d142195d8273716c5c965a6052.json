{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport RadioButtonUncheckedIcon from '../internal/svg-icons/RadioButtonUnchecked';\nimport RadioButtonCheckedIcon from '../internal/svg-icons/RadioButtonChecked';\nimport styled, { rootShouldForwardProp } from '../styles/styled';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst RadioButtonIconRoot = styled('span', {\n  name: 'MuiRadioButtonIcon',\n  shouldForwardProp: rootShouldForwardProp\n})({\n  position: 'relative',\n  display: 'flex'\n});\nconst RadioButtonIconBackground = styled(RadioButtonUncheckedIcon, {\n  name: 'MuiRadioButtonIcon'\n})({\n  // Scale applied to prevent dot misalignment in Safari\n  transform: 'scale(1)'\n});\nconst RadioButtonIconDot = styled(RadioButtonCheckedIcon, {\n  name: 'MuiRadioButtonIcon'\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  left: 0,\n  position: 'absolute',\n  transform: 'scale(0)',\n  transition: theme.transitions.create('transform', {\n    easing: theme.transitions.easing.easeIn,\n    duration: theme.transitions.duration.shortest\n  })\n}, ownerState.checked && {\n  transform: 'scale(1)',\n  transition: theme.transitions.create('transform', {\n    easing: theme.transitions.easing.easeOut,\n    duration: theme.transitions.duration.shortest\n  })\n}));\n\n/**\n * @ignore - internal component.\n */\nfunction RadioButtonIcon(props) {\n  const {\n    checked = false,\n    classes = {},\n    fontSize\n  } = props;\n  const ownerState = _extends({}, props, {\n    checked\n  });\n  return /*#__PURE__*/_jsxs(RadioButtonIconRoot, {\n    className: classes.root,\n    ownerState: ownerState,\n    children: [/*#__PURE__*/_jsx(RadioButtonIconBackground, {\n      fontSize: fontSize,\n      className: classes.background,\n      ownerState: ownerState\n    }), /*#__PURE__*/_jsx(RadioButtonIconDot, {\n      fontSize: fontSize,\n      className: classes.dot,\n      ownerState: ownerState\n    })]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? RadioButtonIcon.propTypes = {\n  /**\n   * If `true`, the component is checked.\n   */\n  checked: PropTypes.bool,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * The size of the component.\n   * `small` is equivalent to the dense radio styling.\n   */\n  fontSize: PropTypes.oneOf(['small', 'medium'])\n} : void 0;\nexport default RadioButtonIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}