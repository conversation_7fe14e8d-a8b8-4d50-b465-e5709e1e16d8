# Database Service Refactoring Plan

*Last Updated: June 26, 2025*  
*Based on: Authentication service separation and database-service cleanup requirements*

## 🎯 **Overview**

This document outlines the plan for refactoring the Database Service to remove authentication functionality and integrate with the new standalone Authentication Service.

## 🚨 **Critical Constraint**

**⚠️ NEVER MODIFY DATABASE-SERVICE DIRECTLY ⚠️**

Based on lessons learned, all changes must be:
- **Planned thoroughly** before implementation
- **Tested in isolation** before deployment
- **Coordinated carefully** to avoid breaking existing functionality
- **Documented comprehensively** for future reference

---

## 📋 **Current State Analysis**

### **Database Service Authentication Components** 🔍

#### **C++ Components to Remove**
```cpp
// In database-service/src/security/security_manager.cpp
class SecurityManager {
    // Authentication methods to be removed:
    std::expected<TokenPair, SecurityError> authenticate(username, password);
    std::expected<User, SecurityError> createUser(username, password, isAdmin);
    std::expected<void, SecurityError> hashPassword(password);
    // JWT token management
    // User session management
    // Password validation
};
```

#### **Database Tables to Migrate**
```sql
-- Tables to move to auth-service database:
users (id, username, password_hash, is_admin, active, created_at, last_login)
user_sessions (session_id, user_id, created_at, expires_at, ip_address)
audit_logs (id, user_id, action, timestamp, ip_address, details)
```

#### **API Endpoints to Remove**
```cpp
// HTTP endpoints to be removed from database-service:
POST /auth/login
POST /auth/logout  
POST /auth/refresh
GET /auth/validate
POST /users (user creation)
GET /users (user listing)
PUT /users/{id} (user updates)
DELETE /users/{id} (user deletion)
```

#### **UI Components to Remove**
```typescript
// React components to move to auth-service-ui:
- LoginForm.tsx
- UserManagement.tsx
- UserList.tsx
- UserEdit.tsx
- AuthContext.tsx
- useAuth.ts
- Authentication routing
- User administration pages
```

---

## 🔄 **Refactoring Strategy**

### **Phase 1: Authentication Service Completion** ⏳ **PREREQUISITE**
**Objective**: Ensure auth-service is fully functional before database-service changes

#### **Requirements Before Database-Service Changes**:
- [ ] Auth-service fully implemented (Phase 3 complete)
- [ ] JWT token validation working
- [ ] User management API functional
- [ ] Auth-service UI operational
- [ ] Integration testing passed
- [ ] Production deployment successful

### **Phase 2: Database Service Integration** ⏳ **PLANNED**
**Objective**: Modify database-service to validate tokens from auth-service

#### **Step 2.1: Add JWT Validation**
```cpp
// New component: JWTValidator
class JWTValidator {
public:
    std::expected<UserClaims, ValidationError> validateToken(const std::string& token);
    bool isTokenExpired(const std::string& token);
    std::optional<std::string> extractUserId(const std::string& token);
    
private:
    std::string auth_service_public_key_;
    std::string auth_service_url_;
};
```

#### **Step 2.2: Replace SecurityManager Authentication**
```cpp
// Modified SecurityManager (authentication removed)
class SecurityManager {
public:
    // REMOVED: authenticate(), createUser(), hashPassword()
    // ADDED: JWT validation only
    std::expected<UserClaims, SecurityError> validateRequest(const HttpRequest& request);
    bool hasPermission(const UserClaims& user, const std::string& resource);
    
private:
    std::unique_ptr<JWTValidator> jwt_validator_;
};
```

#### **Step 2.3: Update API Middleware**
```cpp
// Modified authentication middleware
class AuthenticationMiddleware {
public:
    bool authenticate(HttpRequest& request) {
        auto auth_header = request.getHeader("Authorization");
        if (!auth_header || !auth_header->starts_with("Bearer ")) {
            return false;
        }
        
        auto token = auth_header->substr(7);
        auto claims = jwt_validator_->validateToken(token);
        if (!claims) {
            return false;
        }
        
        request.setUserClaims(claims.value());
        return true;
    }
};
```

### **Phase 3: Database Migration** ⏳ **PLANNED**
**Objective**: Move user data from database-service to auth-service

#### **Step 3.1: Data Export**
```sql
-- Export existing users from database-service
SELECT id, username, password_hash, is_admin, active, created_at, last_login
FROM users
ORDER BY id;

-- Export audit logs
SELECT id, user_id, action, timestamp, ip_address, details
FROM audit_logs
ORDER BY timestamp;
```

#### **Step 3.2: Data Import to Auth-Service**
```sql
-- Import users to auth-service database
INSERT INTO auth_service.users (id, username, password_hash, is_admin, active, created_at, last_login)
SELECT id, username, password_hash, is_admin, active, created_at, last_login
FROM database_service.users;

-- Import audit logs
INSERT INTO auth_service.audit_logs (id, user_id, action, timestamp, ip_address, details)
SELECT id, user_id, action, timestamp, ip_address, details
FROM database_service.audit_logs;
```

#### **Step 3.3: Database Cleanup**
```sql
-- Remove authentication tables from database-service (AFTER VERIFICATION)
-- DROP TABLE user_sessions;
-- DROP TABLE audit_logs;
-- DROP TABLE users;
-- Note: Only after confirming auth-service is working correctly
```

### **Phase 4: UI Refactoring** ⏳ **PLANNED**
**Objective**: Remove authentication UI from database-service, integrate with auth-service

#### **Step 4.1: Remove Authentication Components**
```typescript
// Components to remove from database-service-ui:
- src/contexts/AuthContext.tsx
- src/contexts/useAuth.ts
- src/components/auth/LoginForm.tsx
- src/components/users/UserManagement.tsx
- src/pages/Login.tsx
- src/pages/Users.tsx
```

#### **Step 4.2: Add Auth-Service Integration**
```typescript
// New auth integration for database-service-ui:
class AuthServiceClient {
    async validateToken(token: string): Promise<UserClaims> {
        const response = await fetch(`${AUTH_SERVICE_URL}/auth/validate`, {
            method: 'POST',
            headers: { 'Authorization': `Bearer ${token}` }
        });
        return response.json();
    }
    
    redirectToLogin(): void {
        window.location.href = `${AUTH_SERVICE_UI_URL}/login?redirect=${encodeURIComponent(window.location.href)}`;
    }
}
```

#### **Step 4.3: Update Routing**
```typescript
// Modified routing to redirect to auth-service for authentication
const ProtectedRoute: React.FC<{children: React.ReactNode}> = ({ children }) => {
    const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
    
    useEffect(() => {
        const token = localStorage.getItem('auth_token');
        if (!token) {
            authService.redirectToLogin();
            return;
        }
        
        authService.validateToken(token)
            .then(() => setIsAuthenticated(true))
            .catch(() => authService.redirectToLogin());
    }, []);
    
    if (isAuthenticated === null) return <LoadingSpinner />;
    return <>{children}</>;
};
```

---

## 🔗 **Integration Architecture**

### **Service Communication**
```
┌─────────────────┐    JWT Token    ┌─────────────────┐
│   Auth Service  │◄────────────────│ Database Service│
│   Port: 8082    │                 │   Port: 8081    │
└─────────────────┘                 └─────────────────┘
         │                                   │
         │                                   │
    ┌─────────┐                         ┌─────────┐
    │Auth UI  │                         │ DB UI   │
    │Port:3001│                         │Port:3000│
    └─────────┘                         └─────────┘
```

### **Authentication Flow**
1. **User Access**: User tries to access database-service UI
2. **Token Check**: Database-service checks for valid JWT token
3. **Redirect**: If no token, redirect to auth-service login
4. **Authentication**: User authenticates with auth-service
5. **Token Return**: Auth-service provides JWT token
6. **Service Access**: User accesses database-service with valid token
7. **Token Validation**: Database-service validates token with auth-service

---

## 📋 **Migration Checklist**

### **Pre-Migration Requirements** ⏳
- [ ] Auth-service fully functional and tested
- [ ] Auth-service UI operational
- [ ] JWT token system working correctly
- [ ] Integration testing completed
- [ ] Backup of all database-service data
- [ ] Rollback plan prepared

### **Migration Steps** ⏳
- [ ] Deploy auth-service to production
- [ ] Export user data from database-service
- [ ] Import user data to auth-service
- [ ] Verify data integrity
- [ ] Update database-service to use JWT validation
- [ ] Test integration between services
- [ ] Update database-service UI
- [ ] Remove authentication components from database-service
- [ ] Clean up database tables (after verification)

### **Post-Migration Verification** ⏳
- [ ] All existing users can authenticate
- [ ] Database-service functionality unchanged
- [ ] UI integration working correctly
- [ ] Performance acceptable
- [ ] Security audit passed
- [ ] Documentation updated

---

## 🚨 **Risk Mitigation**

### **High-Risk Areas**
1. **Data Loss**: User accounts and audit logs
2. **Service Downtime**: During migration process
3. **Integration Failures**: JWT validation issues
4. **UI Breakage**: Authentication flow disruption

### **Mitigation Strategies**
1. **Complete Backups**: Full database and configuration backups
2. **Staged Deployment**: Test in development environment first
3. **Rollback Plan**: Ability to revert to original state
4. **Monitoring**: Comprehensive logging during migration
5. **User Communication**: Notify users of planned changes

---

## 📊 **Success Criteria**

### **Technical Success**
- [ ] Auth-service handles all authentication
- [ ] Database-service validates JWT tokens correctly
- [ ] No authentication functionality remains in database-service
- [ ] All existing users can access both services
- [ ] Performance impact minimal

### **User Experience Success**
- [ ] Seamless single sign-on between services
- [ ] No disruption to existing workflows
- [ ] Improved security with centralized authentication
- [ ] Clear separation of concerns

### **Operational Success**
- [ ] Simplified deployment and maintenance
- [ ] Independent service scaling
- [ ] Improved security monitoring
- [ ] Reduced code complexity

---

## 🔄 **Timeline**

### **Phase 1: Auth-Service Completion** (Estimated: 4-6 weeks)
- Complete auth-service implementation
- Deploy and test auth-service
- Verify all functionality working

### **Phase 2: Integration Development** (Estimated: 2-3 weeks)
- Implement JWT validation in database-service
- Update API middleware
- Test integration

### **Phase 3: Data Migration** (Estimated: 1 week)
- Export/import user data
- Verify data integrity
- Test user authentication

### **Phase 4: UI Updates** (Estimated: 2-3 weeks)
- Remove auth components from database-service UI
- Implement auth-service integration
- Test complete user workflows

### **Phase 5: Cleanup** (Estimated: 1 week)
- Remove unused code
- Clean up database tables
- Update documentation

---

*This refactoring plan ensures a safe, systematic approach to separating authentication concerns while maintaining system reliability and user experience.*
