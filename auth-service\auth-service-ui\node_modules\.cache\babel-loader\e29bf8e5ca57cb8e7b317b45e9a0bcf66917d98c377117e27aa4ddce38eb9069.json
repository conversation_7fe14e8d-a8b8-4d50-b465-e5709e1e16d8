{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"onEnter\", \"onExited\"],\n  _excluded2 = [\"action\", \"anchorOrigin\", \"autoHideDuration\", \"children\", \"className\", \"ClickAwayListenerProps\", \"ContentProps\", \"disableWindowBlurListener\", \"message\", \"onBlur\", \"onClose\", \"onFocus\", \"onMouseEnter\", \"onMouseLeave\", \"open\", \"resumeHideDuration\", \"TransitionComponent\", \"transitionDuration\", \"TransitionProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '@mui/utils/composeClasses';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport useSnackbar from './useSnackbar';\nimport ClickAwayListener from '../ClickAwayListener';\nimport { styled, useTheme } from '../styles';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport capitalize from '../utils/capitalize';\nimport Grow from '../Grow';\nimport SnackbarContent from '../SnackbarContent';\nimport { getSnackbarUtilityClass } from './snackbarClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    anchorOrigin\n  } = ownerState;\n  const slots = {\n    root: ['root', `anchorOrigin${capitalize(anchorOrigin.vertical)}${capitalize(anchorOrigin.horizontal)}`]\n  };\n  return composeClasses(slots, getSnackbarUtilityClass, classes);\n};\nconst SnackbarRoot = styled('div', {\n  name: 'MuiSnackbar',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[`anchorOrigin${capitalize(ownerState.anchorOrigin.vertical)}${capitalize(ownerState.anchorOrigin.horizontal)}`]];\n  }\n})(({\n  theme,\n  ownerState\n}) => {\n  const center = {\n    left: '50%',\n    right: 'auto',\n    transform: 'translateX(-50%)'\n  };\n  return _extends({\n    zIndex: (theme.vars || theme).zIndex.snackbar,\n    position: 'fixed',\n    display: 'flex',\n    left: 8,\n    right: 8,\n    justifyContent: 'center',\n    alignItems: 'center'\n  }, ownerState.anchorOrigin.vertical === 'top' ? {\n    top: 8\n  } : {\n    bottom: 8\n  }, ownerState.anchorOrigin.horizontal === 'left' && {\n    justifyContent: 'flex-start'\n  }, ownerState.anchorOrigin.horizontal === 'right' && {\n    justifyContent: 'flex-end'\n  }, {\n    [theme.breakpoints.up('sm')]: _extends({}, ownerState.anchorOrigin.vertical === 'top' ? {\n      top: 24\n    } : {\n      bottom: 24\n    }, ownerState.anchorOrigin.horizontal === 'center' && center, ownerState.anchorOrigin.horizontal === 'left' && {\n      left: 24,\n      right: 'auto'\n    }, ownerState.anchorOrigin.horizontal === 'right' && {\n      right: 24,\n      left: 'auto'\n    })\n  });\n});\nconst Snackbar = /*#__PURE__*/React.forwardRef(function Snackbar(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiSnackbar'\n  });\n  const theme = useTheme();\n  const defaultTransitionDuration = {\n    enter: theme.transitions.duration.enteringScreen,\n    exit: theme.transitions.duration.leavingScreen\n  };\n  const {\n      action,\n      anchorOrigin: {\n        vertical,\n        horizontal\n      } = {\n        vertical: 'bottom',\n        horizontal: 'left'\n      },\n      autoHideDuration = null,\n      children,\n      className,\n      ClickAwayListenerProps,\n      ContentProps,\n      disableWindowBlurListener = false,\n      message,\n      open,\n      TransitionComponent = Grow,\n      transitionDuration = defaultTransitionDuration,\n      TransitionProps: {\n        onEnter,\n        onExited\n      } = {}\n    } = props,\n    TransitionProps = _objectWithoutPropertiesLoose(props.TransitionProps, _excluded),\n    other = _objectWithoutPropertiesLoose(props, _excluded2);\n  const ownerState = _extends({}, props, {\n    anchorOrigin: {\n      vertical,\n      horizontal\n    },\n    autoHideDuration,\n    disableWindowBlurListener,\n    TransitionComponent,\n    transitionDuration\n  });\n  const classes = useUtilityClasses(ownerState);\n  const {\n    getRootProps,\n    onClickAway\n  } = useSnackbar(_extends({}, ownerState));\n  const [exited, setExited] = React.useState(true);\n  const rootProps = useSlotProps({\n    elementType: SnackbarRoot,\n    getSlotProps: getRootProps,\n    externalForwardedProps: other,\n    ownerState,\n    additionalProps: {\n      ref\n    },\n    className: [classes.root, className]\n  });\n  const handleExited = node => {\n    setExited(true);\n    if (onExited) {\n      onExited(node);\n    }\n  };\n  const handleEnter = (node, isAppearing) => {\n    setExited(false);\n    if (onEnter) {\n      onEnter(node, isAppearing);\n    }\n  };\n\n  // So we only render active snackbars.\n  if (!open && exited) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(ClickAwayListener, _extends({\n    onClickAway: onClickAway\n  }, ClickAwayListenerProps, {\n    children: /*#__PURE__*/_jsx(SnackbarRoot, _extends({}, rootProps, {\n      children: /*#__PURE__*/_jsx(TransitionComponent, _extends({\n        appear: true,\n        in: open,\n        timeout: transitionDuration,\n        direction: vertical === 'top' ? 'down' : 'up',\n        onEnter: handleEnter,\n        onExited: handleExited\n      }, TransitionProps, {\n        children: children || /*#__PURE__*/_jsx(SnackbarContent, _extends({\n          message: message,\n          action: action\n        }, ContentProps))\n      }))\n    }))\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Snackbar.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The action to display. It renders after the message, at the end of the snackbar.\n   */\n  action: PropTypes.node,\n  /**\n   * The anchor of the `Snackbar`.\n   * On smaller screens, the component grows to occupy all the available width,\n   * the horizontal alignment is ignored.\n   * @default { vertical: 'bottom', horizontal: 'left' }\n   */\n  anchorOrigin: PropTypes.shape({\n    horizontal: PropTypes.oneOf(['center', 'left', 'right']).isRequired,\n    vertical: PropTypes.oneOf(['bottom', 'top']).isRequired\n  }),\n  /**\n   * The number of milliseconds to wait before automatically calling the\n   * `onClose` function. `onClose` should then set the state of the `open`\n   * prop to hide the Snackbar. This behavior is disabled by default with\n   * the `null` value.\n   * @default null\n   */\n  autoHideDuration: PropTypes.number,\n  /**\n   * Replace the `SnackbarContent` component.\n   */\n  children: PropTypes.element,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Props applied to the `ClickAwayListener` element.\n   */\n  ClickAwayListenerProps: PropTypes.object,\n  /**\n   * Props applied to the [`SnackbarContent`](/material-ui/api/snackbar-content/) element.\n   */\n  ContentProps: PropTypes.object,\n  /**\n   * If `true`, the `autoHideDuration` timer will expire even if the window is not focused.\n   * @default false\n   */\n  disableWindowBlurListener: PropTypes.bool,\n  /**\n   * When displaying multiple consecutive snackbars using a single parent-rendered\n   * `<Snackbar/>`, add the `key` prop to ensure independent treatment of each message.\n   * For instance, use `<Snackbar key={message} />`. Otherwise, messages might update\n   * in place, and features like `autoHideDuration` could be affected.\n   */\n  key: () => null,\n  /**\n   * The message to display.\n   */\n  message: PropTypes.node,\n  /**\n   * @ignore\n   */\n  onBlur: PropTypes.func,\n  /**\n   * Callback fired when the component requests to be closed.\n   * Typically `onClose` is used to set state in the parent component,\n   * which is used to control the `Snackbar` `open` prop.\n   * The `reason` parameter can optionally be used to control the response to `onClose`,\n   * for example ignoring `clickaway`.\n   *\n   * @param {React.SyntheticEvent<any> | Event} event The event source of the callback.\n   * @param {string} reason Can be: `\"timeout\"` (`autoHideDuration` expired), `\"clickaway\"`, or `\"escapeKeyDown\"`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocus: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onMouseEnter: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onMouseLeave: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool,\n  /**\n   * The number of milliseconds to wait before dismissing after user interaction.\n   * If `autoHideDuration` prop isn't specified, it does nothing.\n   * If `autoHideDuration` prop is specified but `resumeHideDuration` isn't,\n   * we default to `autoHideDuration / 2` ms.\n   */\n  resumeHideDuration: PropTypes.number,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The component used for the transition.\n   * [Follow this guide](/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.\n   * @default Grow\n   */\n  TransitionComponent: PropTypes.elementType,\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   * @default {\n   *   enter: theme.transitions.duration.enteringScreen,\n   *   exit: theme.transitions.duration.leavingScreen,\n   * }\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * Props applied to the transition element.\n   * By default, the element is based on this [`Transition`](https://reactcommunity.org/react-transition-group/transition/) component.\n   * @default {}\n   */\n  TransitionProps: PropTypes.object\n} : void 0;\nexport default Snackbar;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}