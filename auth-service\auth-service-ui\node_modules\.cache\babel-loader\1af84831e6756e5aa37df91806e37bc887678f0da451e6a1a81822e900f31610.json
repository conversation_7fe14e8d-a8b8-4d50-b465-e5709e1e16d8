{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport setRef from '../setRef';\nexport default function useForkRef(...refs) {\n  /**\n   * This will create a new function if the refs passed to this hook change and are all defined.\n   * This means react will call the old forkRef with `null` and the new forkRef\n   * with the ref. Cleanup naturally emerges from this behavior.\n   */\n  return React.useMemo(() => {\n    if (refs.every(ref => ref == null)) {\n      return null;\n    }\n    return instance => {\n      refs.forEach(ref => {\n        setRef(ref, instance);\n      });\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, refs);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}