﻿#include "user_manager.hpp"
#include "database_manager.hpp"
#include "security_manager.hpp"
#include <iostream>
#include <regex>

UserManager::UserManager(DatabaseManager* db_manager, SecurityManager* sec_manager)
    : db_manager_(db_manager), sec_manager_(sec_manager) {
    std::cout << "UserManager initialized with database integration" << std::endl;
}

UserManager::~UserManager() = default;

std::string UserManager::create_user(const std::string& username, const std::string& email, const std::string& password) {
    std::cout << "Creating user with database integration: " << username << std::endl;

    // Validate input
    if (!is_valid_username(username)) {
        std::cout << "Invalid username format: " << username << std::endl;
        return "";
    }

    if (!is_valid_email(email)) {
        std::cout << "Invalid email format: " << email << std::endl;
        return "";
    }

    if (password.length() < 8) {
        std::cout << "Password too short (minimum 8 characters)" << std::endl;
        return "";
    }

    // Hash the password with salt
    std::string hashed_password = sec_manager_->hash_password(password);
    std::string salt = "generated_salt"; // In production, generate proper salt

    // Create user in database
    std::string user_id = db_manager_->create_user(username, email, hashed_password, salt);

    if (!user_id.empty()) {
        std::cout << "User created successfully: " << username << " (ID: " << user_id << ")" << std::endl;
    } else {
        std::cout << "Failed to create user: " << username << std::endl;
    }

    return user_id;
}

bool UserManager::create_user(const std::string& username, const std::string& password) {
    // Legacy method - generate email from username
    std::string email = username + "@example.com";
    std::string user_id = create_user(username, email, password);
    return !user_id.empty();
}

UserManager::AuthResult UserManager::authenticate_user(const std::string& username, const std::string& password) {
    std::cout << "Authenticating user with database lookup: " << username << std::endl;

    AuthResult result;
    result.success = false;
    result.user_id = "";

    // Get user from database
    auto user_opt = db_manager_->get_user_by_username(username);

    if (!user_opt.has_value()) {
        result.error_message = "User not found";
        std::cout << "Authentication failed - user not found: " << username << std::endl;
        return result;
    }

    auto user = user_opt.value();

    // Verify password
    bool password_valid = sec_manager_->verify_password(password, user.password_hash);

    if (password_valid) {
        result.success = true;
        result.user_id = user.user_id;
        result.username = user.username;
        result.email = user.email;

        // Update last login time
        db_manager_->update_user_last_login(user.user_id);

        std::cout << "User authenticated successfully: " << username << " (ID: " << user.user_id << ")" << std::endl;
    } else {
        result.error_message = "Invalid password";
        std::cout << "Authentication failed - invalid password: " << username << std::endl;
    }

    return result;
}

bool UserManager::authenticate_user_simple(const std::string& username, const std::string& password) {
    auto result = authenticate_user(username, password);
    return result.success;
}

std::string UserManager::get_user_id(const std::string& username) {
    auto user_opt = db_manager_->get_user_by_username(username);
    if (user_opt.has_value()) {
        return user_opt.value().user_id;
    }
    return "";
}

bool UserManager::update_last_login(const std::string& user_id) {
    return db_manager_->update_user_last_login(user_id);
}

bool UserManager::delete_user(const std::string& username) {
    std::cout << "Deleting user: " << username << std::endl;

    // Get user ID first
    std::string user_id = get_user_id(username);
    if (user_id.empty()) {
        std::cout << "User not found: " << username << std::endl;
        return false;
    }

    return delete_user_by_id(user_id);
}

bool UserManager::delete_user_by_id(const std::string& user_id) {
    bool success = db_manager_->delete_user(user_id);

    if (success) {
        std::cout << "User deleted successfully (ID: " << user_id << ")" << std::endl;
    } else {
        std::cout << "Failed to delete user (ID: " << user_id << ")" << std::endl;
    }

    return success;
}

UserManager::CreateUserResult UserManager::createUser(const std::string& username, const std::string& email,
                                                      const std::string& password, const std::string& first_name,
                                                      const std::string& last_name) {
    CreateUserResult result;
    result.success = false;

    std::cout << "Creating user with admin API: " << username << std::endl;

    // Validate input
    if (!is_valid_username(username)) {
        result.error_message = "Invalid username format";
        std::cout << "Invalid username format: " << username << std::endl;
        return result;
    }

    if (!is_valid_email(email)) {
        result.error_message = "Invalid email format";
        std::cout << "Invalid email format: " << email << std::endl;
        return result;
    }

    if (password.length() < 8) {
        result.error_message = "Password too short (minimum 8 characters)";
        std::cout << "Password too short (minimum 8 characters)" << std::endl;
        return result;
    }

    // Check if user already exists
    auto existing_user = db_manager_->get_user_by_username(username);
    if (existing_user.has_value()) {
        result.error_message = "Username already exists";
        std::cout << "Username already exists: " << username << std::endl;
        return result;
    }

    // Hash the password with salt
    std::string hashed_password = sec_manager_->hash_password(password);
    std::string salt = "generated_salt"; // In production, generate proper salt

    // Create user in database
    std::string user_id = db_manager_->create_user(username, email, hashed_password, salt);

    if (!user_id.empty()) {
        result.success = true;
        result.user_id = user_id;
        std::cout << "User created successfully: " << username << " (ID: " << user_id << ")" << std::endl;
    } else {
        result.error_message = "Failed to create user in database";
        std::cout << "Failed to create user: " << username << std::endl;
    }

    return result;
}

std::vector<UserManager::UserInfo> UserManager::getAllUsers() {
    std::vector<UserInfo> users;

    try {
        // TODO: Implement a proper getAllUsers method in DatabaseManager
        // For now, we'll return mock data since the database doesn't have this method yet

        std::cout << "Getting all users (using mock data for now)" << std::endl;

        // Mock data for demonstration
        UserInfo user1;
        user1.user_id = "123e4567-e89b-12d3-a456-426614174000";
        user1.username = "testuser";
        user1.email = "<EMAIL>";
        user1.first_name = "Test";
        user1.last_name = "User";
        user1.is_active = true;
        user1.email_verified = false;
        user1.is_system_admin = false;
        user1.created_at = std::chrono::system_clock::now() - std::chrono::hours(24);
        user1.last_login = std::chrono::system_clock::now() - std::chrono::hours(2);
        users.push_back(user1);

        UserInfo user2;
        user2.user_id = "123e4567-e89b-12d3-a456-426614174001";
        user2.username = "btaylor-admin";
        user2.email = "<EMAIL>";
        user2.first_name = "Brian";
        user2.last_name = "Taylor";
        user2.is_active = true;
        user2.email_verified = true;
        user2.is_system_admin = true;
        user2.created_at = std::chrono::system_clock::now() - std::chrono::hours(168); // 1 week ago
        user2.last_login = std::chrono::system_clock::now() - std::chrono::minutes(30);
        users.push_back(user2);

        std::cout << "Returning " << users.size() << " users" << std::endl;

    } catch (const std::exception& e) {
        std::cout << "Error getting all users: " << e.what() << std::endl;
    }

    return users;
}

bool UserManager::is_valid_email(const std::string& email) {
    // Simple email validation regex
    const std::regex email_pattern(R"([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})");
    return std::regex_match(email, email_pattern);
}

bool UserManager::is_valid_username(const std::string& username) {
    // Username: 3-50 characters, alphanumeric and underscore only
    if (username.length() < 3 || username.length() > 50) {
        return false;
    }

    const std::regex username_pattern(R"([a-zA-Z0-9_]+)");
    return std::regex_match(username, username_pattern);
}
