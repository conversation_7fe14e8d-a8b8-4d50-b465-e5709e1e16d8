<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auth Service</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 50%, #2a3441 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            overflow: hidden;
        }
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 20% 80%, rgba(0, 168, 255, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(0, 120, 212, 0.1) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }
        .auth-container {
            background: rgba(26, 31, 46, 0.9);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3),
                        0 0 0 1px rgba(0, 168, 255, 0.1),
                        inset 0 1px 0 rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(0, 168, 255, 0.2);
            width: 100%;
            max-width: 420px;
            padding: 50px;
            position: relative;
            overflow: hidden;
        }
        .auth-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 20px;
            background: linear-gradient(135deg, rgba(0, 168, 255, 0.05) 0%, rgba(0, 120, 212, 0.02) 100%);
            pointer-events: none;
        }
        .logo {
            text-align: center;
            margin-bottom: 35px;
        }
        .logo h1 {
            color: #ffffff;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 0 20px rgba(0, 168, 255, 0.3);
        }
        .logo p {
            color: rgba(255, 255, 255, 0.7);
            font-size: 15px;
            opacity: 0.9;
        }
        .form-group {
            margin-bottom: 24px;
            position: relative;
        }
        .form-group label {
            position: absolute;
            top: -8px;
            left: 12px;
            background: rgba(26, 31, 46, 0.3);
            backdrop-filter: blur(15px);
            padding: 0 8px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
            font-size: 14px;
            z-index: 1;
            border-radius: 4px;
            border: 1px solid rgba(0, 168, 255, 0.2);
        }
        .form-group input {
            width: 100%;
            padding: 16px 18px 16px 50px;
            background: rgba(42, 52, 65, 0.5);
            border: 2px solid rgba(0, 168, 255, 0.2);
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            color: #ffffff;
        }
        .form-group input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }
        .form-group input:focus {
            outline: none;
            border-color: #00a8ff;
            box-shadow: 0 0 0 3px rgba(0, 168, 255, 0.2),
                        0 0 20px rgba(0, 168, 255, 0.1);
            background: rgba(42, 52, 65, 0.7);
        }
        .form-group .icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(0, 168, 255, 0.7);
            z-index: 2;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .form-group .icon svg {
            width: 20px;
            height: 20px;
        }
        .demo-credentials {
            margin-top: 30px;
            padding: 20px;
            background: rgba(42, 52, 65, 0.3);
            border-radius: 8px;
            border: 1px solid rgba(0, 168, 255, 0.2);
        }
        .demo-credentials h4 {
            color: #ffffff;
            margin-bottom: 12px;
            font-size: 14px;
        }
        .demo-credentials p {
            color: rgba(255, 255, 255, 0.7);
            margin: 6px 0;
            font-size: 13px;
        }
        .btn {
            width: 100%;
            padding: 16px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 14px;
        }
        .btn-primary {
            background: linear-gradient(135deg, #00a8ff 0%, #0078d4 100%);
            color: white;
            box-shadow: 0 4px 14px rgba(0, 168, 255, 0.4),
                        0 0 20px rgba(0, 168, 255, 0.2);
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 168, 255, 0.6),
                        0 0 30px rgba(0, 168, 255, 0.3);
            background: linear-gradient(135deg, #0078d4 0%, #005a9f 100%);
        }
        .alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin: 16px 0;
            font-size: 14px;
            font-weight: 500;
        }
        .alert-success {
            background: rgba(0, 168, 255, 0.1);
            color: #00a8ff;
            border: 1px solid rgba(0, 168, 255, 0.3);
        }
        .alert-error {
            background: rgba(255, 99, 99, 0.1);
            color: #ff6363;
            border: 1px solid rgba(255, 99, 99, 0.3);
        }
        .footer {
            text-align: center;
            margin-top: 35px;
            padding-top: 25px;
            border-top: 1px solid rgba(0, 168, 255, 0.2);
            color: rgba(255, 255, 255, 0.5);
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="logo">
            <div style="font-size: 48px; color: #00a8ff; margin-bottom: 20px;">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M18,8h-1V6c0-2.76-2.24-5-5-5S7,3.24,7,6v2H6c-1.1,0-2,0.9-2,2v10c0,1.1,0.9,2,2,2h12c1.1,0,2-0.9,2-2V10C20,8.9,19.1,8,18,8z M12,17c-1.1,0-2-0.9-2-2s0.9-2,2-2s2,0.9,2,2S13.1,17,12,17z M15.1,8H8.9V6c0-1.71,1.39-3.1,3.1-3.1s3.1,1.39,3.1,3.1V8z"/>
                </svg>
            </div>
            <h1>OAuth 2.0 Service</h1>
            <p>Secure Authentication Dashboard</p>
        </div>

        <form onsubmit="handleLogin(event)">
            <div class="form-group">
                <span class="icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"/>
                    </svg>
                </span>
                <label for="username">Username</label>
                <input type="text" id="username" value="testuser" required>
            </div>

            <div class="form-group">
                <span class="icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z"/>
                    </svg>
                </span>
                <label for="password">Password</label>
                <input type="password" id="password" value="testpass123" required>
            </div>

            <button type="submit" class="btn btn-primary">Sign In</button>
        </form>

        <div style="text-align: center; margin-top: 20px;">
            <a href="#" style="color: #00a8ff; text-decoration: none; font-size: 14px;">Forgot Password?</a>
        </div>

        <div id="login-message"></div>

        <div class="demo-credentials">
            <h4>Test Credentials</h4>
            <p>Username: testuser</p>
            <p>Password: testpass123</p>
        </div>
    </div>

    <script>
        async function handleLogin(event) {
            event.preventDefault();
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            try {
                const response = await fetch('/oauth/token', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        username: username,
                        password: password,
                        grant_type: 'password'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log('OAuth Token:', data);

                    // Store token and user info
                    localStorage.setItem('auth_token', data.access_token);
                    localStorage.setItem('refresh_token', data.refresh_token || '');
                    localStorage.setItem('user_info', JSON.stringify({
                        username: username,
                        login_time: new Date().toISOString(),
                        token_expires: data.expires_in,
                        is_admin: data.is_admin || false,
                        permissions: data.permissions || []
                    }));

                    // Check if user is admin and redirect accordingly
                    // Since OAuth response doesn't include is_admin flag, check username
                    if (username === 'btaylor-admin') {
                        console.log('Admin user detected, redirecting to admin dashboard');
                        window.location.href = '/admin.html';
                    } else {
                        console.log('Regular user, redirecting to user dashboard');
                        window.location.href = '/dashboard.html';
                    }
                } else {
                    const error = await response.json();
                    showMessage('Authentication failed: ' + (error.error_description || 'Invalid credentials'), 'error');
                }
            } catch (error) {
                showMessage('Connection error: Unable to reach authentication server', 'error');
            }
        }

        function showMessage(message, type) {
            const messageDiv = document.getElementById('login-message');
            messageDiv.innerHTML = '<div class="alert alert-' + type + '">' + message + '</div>';
            setTimeout(() => messageDiv.innerHTML = '', 5000);
        }
    </script>
</body>
</html>
