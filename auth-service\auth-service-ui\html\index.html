<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auth Service - Professional Authentication</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .auth-container {
            background: linear-gradient(135deg, #4a90e2 0%, #7bb3f0 100%);
            border-radius: 16px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
            width: 100%;
            max-width: 420px;
            padding: 50px;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .auth-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3182ce, #2c5282, #2a4365);
        }
        .logo {
            text-align: center;
            margin-bottom: 35px;
        }
        .logo h1 {
            color: #e2e8f0;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .logo p {
            color: #bee3f8;
            font-size: 15px;
            opacity: 0.9;
        }
        .form-group {
            margin-bottom: 24px;
            position: relative;
        }
        .form-group label {
            display: none; /* Hide labels, use placeholders instead */
        }
        .form-group input {
            width: 100%;
            padding: 16px 18px 16px 50px; /* Add left padding for icon */
            background: white;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            color: #333;
        }
        .form-group .icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(0, 0, 0, 0.54);
            font-size: 20px;
        }
        .form-group input::placeholder {
            color: rgba(0, 0, 0, 0.54);
        }
        .form-group input:focus {
            outline: none;
            border-color: #1976d2;
            box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
        }
        .forgot-password {
            text-align: center;
            margin: 16px 0;
        }
        .forgot-password a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: underline;
            font-size: 14px;
            cursor: pointer;
        }
        .forgot-password a:hover {
            color: white;
        }
        .demo-credentials {
            margin-top: 20px;
            padding: 16px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
        }
        .demo-credentials h4 {
            color: white;
            margin-bottom: 8px;
            font-size: 14px;
        }
        .demo-credentials p {
            color: rgba(255, 255, 255, 0.9);
            margin: 4px 0;
            font-size: 14px;
        }
        .btn {
            width: 100%;
            padding: 16px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 14px;
            position: relative;
            overflow: hidden;
        }
        .btn-primary {
            background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%);
            color: white;
            box-shadow: 0 6px 20px rgba(49, 130, 206, 0.4);
        }
        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(49, 130, 206, 0.6);
            background: linear-gradient(135deg, #2c5282 0%, #2a4365 100%);
        }
        .alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin: 16px 0;
            font-size: 14px;
            font-weight: 500;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .alert-success {
            background: rgba(72, 187, 120, 0.2);
            color: #9ae6b4;
            border-color: rgba(72, 187, 120, 0.3);
        }
        .alert-error {
            background: rgba(229, 62, 62, 0.2);
            color: #feb2b2;
            border-color: rgba(229, 62, 62, 0.3);
        }
        .footer {
            text-align: center;
            margin-top: 35px;
            padding-top: 25px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: #a0aec0;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="logo">
            <h1>🔒 OAuth 2.0 Service</h1>
            <p>Secure Authentication Dashboard</p>
        </div>

        <form onsubmit="handleLogin(event)">
            <div class="form-group">
                <span class="icon">👤</span>
                <label for="username">Username</label>
                <input type="text" id="username" placeholder="Username *" required>
            </div>

            <div class="form-group">
                <span class="icon">🔒</span>
                <label for="password">Password</label>
                <input type="password" id="password" placeholder="Password *" required>
            </div>

            <button type="submit" class="btn btn-primary">Sign In</button>

            <div class="forgot-password">
                <a onclick="alert('For password reset, please contact your system administrator')">Forgot Password?</a>
            </div>
        </form>

        <div id="login-message"></div>

        <div style="text-align: center; margin: 16px 0; color: rgba(255, 255, 255, 0.7); font-size: 14px;">
            Test Credentials
        </div>

        <div class="demo-credentials">
            <h4>Demo Credentials</h4>
            <p>Username: <strong>testuser</strong></p>
            <p>Password: <strong>testpass123</strong></p>
        </div>
    </div>

    <script>
        // Set default values and add focus handlers
        document.addEventListener('DOMContentLoaded', function() {
            const usernameField = document.getElementById('username');
            const passwordField = document.getElementById('password');

            // Set default values
            usernameField.value = 'testuser';
            passwordField.value = 'testpass123';

            // Clear defaults on focus
            usernameField.addEventListener('focus', function() {
                if (this.value === 'testuser') {
                    this.value = '';
                }
            });

            passwordField.addEventListener('focus', function() {
                if (this.value === 'testpass123') {
                    this.value = '';
                }
            });
        });

        async function handleLogin(event) {
            event.preventDefault();
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('/oauth/token', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        username: username, 
                        password: password, 
                        grant_type: 'password' 
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log('OAuth Token:', data);
                    
                    // Store token and user info
                    localStorage.setItem('auth_token', data.access_token);
                    localStorage.setItem('refresh_token', data.refresh_token || '');
                    localStorage.setItem('user_info', JSON.stringify({
                        username: username,
                        login_time: new Date().toISOString(),
                        token_expires: data.expires_in,
                        is_admin: data.is_admin || false,
                        permissions: data.permissions || []
                    }));
                    
                    // Check if user is admin and redirect accordingly
                    // Since OAuth response doesn't include is_admin flag, check username
                    if (username === 'btaylor-admin') {
                        console.log('Admin user detected, redirecting to admin dashboard');
                        window.location.href = '/admin.html';
                    } else {
                        console.log('Regular user, redirecting to user dashboard');
                        window.location.href = '/dashboard.html';
                    }
                } else {
                    const error = await response.json();
                    showMessage('Authentication failed: ' + (error.error_description || 'Invalid credentials'), 'error');
                }
            } catch (error) {
                showMessage('Connection error: Unable to reach authentication server', 'error');
                console.error('Auth error:', error);
            }
        }

        function showMessage(message, type) {
            const messageDiv = document.getElementById('login-message');
            messageDiv.innerHTML = '<div class="alert alert-' + type + '">' + message + '</div>';
            setTimeout(() => messageDiv.innerHTML = '', 5000);
        }
    </script>
</body>
</html>
