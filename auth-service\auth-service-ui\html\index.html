<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auth Service</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .auth-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
            width: 100%;
            max-width: 420px;
            padding: 50px;
            position: relative;
            overflow: hidden;
        }
        .auth-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3182ce, #2c5282, #2a4365);
        }
        .logo {
            text-align: center;
            margin-bottom: 35px;
        }
        .logo h1 {
            color: #2d3748;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
        }
        .logo p {
            color: #718096;
            font-size: 15px;
            opacity: 0.9;
        }
        .form-group {
            margin-bottom: 24px;
            position: relative;
        }
        .form-group label {
            position: absolute;
            top: -8px;
            left: 12px;
            background: white;
            padding: 0 8px;
            color: #4a5568;
            font-weight: 500;
            font-size: 14px;
            z-index: 1;
        }
        .form-group input {
            width: 100%;
            padding: 16px 18px 16px 50px;
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            color: #2d3748;
        }
        .form-group input:focus {
            outline: none;
            border-color: #3182ce;
            box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
        }
        .form-group .icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(0, 0, 0, 0.54);
            font-size: 20px;
            z-index: 2;
        }
        .demo-credentials {
            margin-top: 30px;
            padding: 20px;
            background: #f7fafc;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        .demo-credentials h4 {
            color: #2d3748;
            margin-bottom: 12px;
            font-size: 14px;
        }
        .demo-credentials p {
            color: #4a5568;
            margin: 6px 0;
            font-size: 13px;
        }
        .btn {
            width: 100%;
            padding: 16px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 14px;
        }
        .btn-primary {
            background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%);
            color: white;
            box-shadow: 0 4px 14px rgba(49, 130, 206, 0.4);
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(49, 130, 206, 0.6);
        }
        .alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin: 16px 0;
            font-size: 14px;
            font-weight: 500;
        }
        .alert-success {
            background: #c6f6d5;
            color: #22543d;
            border: 1px solid #9ae6b4;
        }
        .alert-error {
            background: #fed7d7;
            color: #742a2a;
            border: 1px solid #feb2b2;
        }
        .footer {
            text-align: center;
            margin-top: 35px;
            padding-top: 25px;
            border-top: 1px solid #e2e8f0;
            color: #a0aec0;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="logo">
            <h1>🔐 Auth Service</h1>
            <p>OAuth 2.0 Authentication</p>
        </div>

        <form onsubmit="handleLogin(event)">
            <div class="form-group">
                <span class="icon">👤</span>
                <label for="username">Username</label>
                <input type="text" id="username" required>
            </div>

            <div class="form-group">
                <span class="icon">🔒</span>
                <label for="password">Password</label>
                <input type="password" id="password" required>
            </div>

            <button type="submit" class="btn btn-primary">Sign In</button>
        </form>

        <div id="login-message"></div>

        <div class="demo-credentials">
            <h4>Test Credentials</h4>
            <p>Username: testuser</p>
            <p>Password: testpass123</p>
        </div>
    </div>

    <script>
        async function handleLogin(event) {
            event.preventDefault();
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            try {
                const response = await fetch('/oauth/token', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        username: username,
                        password: password,
                        grant_type: 'password'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    showMessage('Login successful! Token: ' + data.access_token.substring(0, 20) + '...', 'success');
                } else {
                    const error = await response.json();
                    showMessage('Login failed: ' + (error.error_description || 'Invalid credentials'), 'error');
                }
            } catch (error) {
                showMessage('Connection error: Unable to reach authentication server', 'error');
            }
        }

        function showMessage(message, type) {
            const messageDiv = document.getElementById('login-message');
            messageDiv.innerHTML = '<div class="alert alert-' + type + '">' + message + '</div>';
            setTimeout(() => messageDiv.innerHTML = '', 5000);
        }
    </script>
</body>
</html>
