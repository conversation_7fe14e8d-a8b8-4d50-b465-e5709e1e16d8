﻿#include "auth_service.hpp"
#include "config_manager.hpp"
#include "database_manager.hpp"
#include "security_manager.hpp"
#include "jwt_manager.hpp"
#include "http_server.hpp"
#include "user_manager.hpp"
#include "rbac_manager.hpp"
#include "enhanced_token_manager.hpp"
#include <iostream>

AuthService::AuthService(std::unique_ptr<ConfigManager> config)
    : config_manager_(std::move(config)) {
    database_manager_ = std::make_unique<DatabaseManager>(config_manager_.get());
    security_manager_ = std::make_unique<SecurityManager>(std::shared_ptr<ConfigManager>(config_manager_.get(), [](ConfigManager*){}));
    jwt_manager_ = std::make_unique<JWTManager>(std::shared_ptr<ConfigManager>(config_manager_.get(), [](ConfigManager*){}),
                                               std::shared_ptr<DatabaseManager>(database_manager_.get(), [](DatabaseManager*){}));
    user_manager_ = std::make_unique<UserManager>(database_manager_.get(), security_manager_.get());

    // Initialize RBAC Manager
    rbac_manager_ = std::make_unique<RBACManager>(database_manager_.get());

    // Initialize Enhanced Token Manager
    enhanced_token_manager_ = std::make_unique<EnhancedTokenManager>(database_manager_.get(),
                                                                    rbac_manager_.get(),
                                                                    jwt_manager_.get());

    // Initialize HTTP Server with all components
    http_server_ = std::make_unique<HttpServer>(user_manager_.get(), jwt_manager_.get(),
                                               rbac_manager_.get(), enhanced_token_manager_.get());
}

AuthService::~AuthService() = default;

void AuthService::start(int port) {
    std::cout << "Auth Service starting..." << std::endl;

    // Initialize components
    std::cout << "Initializing database connection..." << std::endl;
    database_manager_->initialize();

    std::cout << "Initializing JWT token management..." << std::endl;
    if (!jwt_manager_->initialize()) {
        std::cerr << "Failed to initialize JWT Manager" << std::endl;
        return;
    }

    std::cout << "Starting HTTP server on port " << port << "..." << std::endl;
    http_server_->start(port);
    std::cout << "Auth Service started on port " << port << std::endl;
}

void AuthService::stop() {
    if (http_server_) http_server_->stop();
}
