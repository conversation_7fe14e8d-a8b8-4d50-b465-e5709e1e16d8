{"environment": "development", "ssh": {"host": "auth-dev.chcit.org", "ip": "***********", "port": 22, "username": "btaylor-admin", "local_key_path": "C:\\Users\\<USER>\\.ssh\\id_rsa"}, "service": {"description": "auth-service for development environment", "name": "auth-service", "user": "auth-service", "group": "auth-service"}, "version": {"created": "2025-06-24 16:15:27", "updated": "2025-06-24 16:15:27", "number": 2}, "database": {"port": 5432, "host": "***********", "name": "auth_service", "user": "auth_service", "password": "VOUGaH&Lr-p6#(oB1r$JoGXk"}, "project": {"remote_install_dir": "/opt/auth-service", "local_source_dir": "D:\\Coding_Projects\\auth-service\\auth-service-app", "name": "auth-service", "description": "auth-service for development environment", "remote_build_dir": "/home/<USER>/auth-service-build"}}