{"ast": null, "code": "// Source from https://github.com/alitaheri/normalize-scroll-left\nlet cachedType;\n\n/**\n * Based on the jquery plugin https://github.com/othree/jquery.rtl-scroll-type\n *\n * Types of scrollLeft, assuming scrollWidth=100 and direction is rtl.\n *\n * Type             | <- Most Left | Most Right -> | Initial\n * ---------------- | ------------ | ------------- | -------\n * default          | 0            | 100           | 100\n * negative (spec*) | -100         | 0             | 0\n * reverse          | 100          | 0             | 0\n *\n * Edge 85: default\n * Safari 14: negative\n * Chrome 85: negative\n * Firefox 81: negative\n * IE11: reverse\n *\n * spec* https://drafts.csswg.org/cssom-view/#dom-window-scroll\n */\nexport function detectScrollType() {\n  if (cachedType) {\n    return cachedType;\n  }\n  const dummy = document.createElement('div');\n  const container = document.createElement('div');\n  container.style.width = '10px';\n  container.style.height = '1px';\n  dummy.appendChild(container);\n  dummy.dir = 'rtl';\n  dummy.style.fontSize = '14px';\n  dummy.style.width = '4px';\n  dummy.style.height = '1px';\n  dummy.style.position = 'absolute';\n  dummy.style.top = '-1000px';\n  dummy.style.overflow = 'scroll';\n  document.body.appendChild(dummy);\n  cachedType = 'reverse';\n  if (dummy.scrollLeft > 0) {\n    cachedType = 'default';\n  } else {\n    dummy.scrollLeft = 1;\n    if (dummy.scrollLeft === 0) {\n      cachedType = 'negative';\n    }\n  }\n  document.body.removeChild(dummy);\n  return cachedType;\n}\n\n// Based on https://stackoverflow.com/a/24394376\nexport function getNormalizedScrollLeft(element, direction) {\n  const scrollLeft = element.scrollLeft;\n\n  // Perform the calculations only when direction is rtl to avoid messing up the ltr behavior\n  if (direction !== 'rtl') {\n    return scrollLeft;\n  }\n  const type = detectScrollType();\n  switch (type) {\n    case 'negative':\n      return element.scrollWidth - element.clientWidth + scrollLeft;\n    case 'reverse':\n      return element.scrollWidth - element.clientWidth - scrollLeft;\n    default:\n      return scrollLeft;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}