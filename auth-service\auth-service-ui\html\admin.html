<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auth Service - Admin Dashboard</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 50%, #2a3441 100%);
            min-height: 100vh;
            color: #ffffff;
            position: relative;
        }
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 20% 80%, rgba(0, 168, 255, 0.05) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(0, 120, 212, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }
        
        .header {
            background: linear-gradient(135deg, rgba(26, 31, 46, 0.9) 0%, rgba(42, 52, 65, 0.9) 100%);
            backdrop-filter: blur(20px);
            color: white;
            padding: 1.5rem 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3),
                        0 0 0 1px rgba(0, 168, 255, 0.1);
            border-bottom: 1px solid rgba(0, 168, 255, 0.2);
        }
        
        .header .btn {
            width: auto;
            padding: 0.75rem 1.5rem;
            font-size: 0.9rem;
            margin: 0;
        }
        
        .header h1 {
            font-size: 1.8rem;
            margin-bottom: 0.5rem;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            opacity: 0.9;
            font-size: 0.95rem;
            color: #bee3f8;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .nav-tabs {
            display: flex;
            background: rgba(26, 54, 93, 0.6);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            margin-bottom: 2rem;
            overflow: hidden;
            border: 1px solid rgba(255,255,255,0.1);
        }
        
        .nav-tab {
            flex: 1;
            padding: 1.2rem;
            text-align: center;
            background: transparent;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            color: #cbd5e0;
            position: relative;
        }
        
        .nav-tab.active {
            background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(49, 130, 206, 0.4);
        }
        
        .nav-tab:hover:not(.active) {
            background: rgba(255,255,255,0.1);
            color: #e2e8f0;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .card {
            background: rgba(26, 54, 93, 0.4);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            padding: 2rem;
            margin-bottom: 1.5rem;
            border: 1px solid rgba(255,255,255,0.1);
        }
        
        .card h3 {
            margin-bottom: 1.5rem;
            color: #e2e8f0;
            font-weight: 600;
            font-size: 1.25rem;
        }
        
        .card h4 {
            color: #cbd5e0;
            margin-bottom: 1rem;
            font-weight: 500;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: rgba(26, 54, 93, 0.6);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            text-align: center;
            border: 1px solid rgba(255,255,255,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.4);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #63b3ed;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .stat-label {
            color: #a0aec0;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .btn {
            padding: 0.875rem 1.75rem;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0.25rem;
            font-size: 0.9rem;
            position: relative;
            overflow: hidden;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(49, 130, 206, 0.4);
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #2c5282 0%, #2a4365 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(49, 130, 206, 0.6);
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: #cbd5e0;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }
        
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            color: #e2e8f0;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 255, 255, 0.1);
        }
        
        .btn-logout {
            background: rgba(255, 255, 255, 0.1);
            color: #a0aec0;
            border: 1px solid rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
        }
        
        .btn-logout:hover {
            background: rgba(255, 255, 255, 0.2);
            color: #cbd5e0;
            border-color: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 255, 255, 0.1);
        }
        
        .token-display {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 1.25rem;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            word-break: break-all;
            margin: 1rem 0;
            color: #e2e8f0;
            backdrop-filter: blur(10px);
        }
        
        .alert {
            padding: 1rem 1.25rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .alert-success {
            background: rgba(72, 187, 120, 0.2);
            color: #9ae6b4;
            border-color: rgba(72, 187, 120, 0.3);
        }
        
        .alert-error {
            background: rgba(229, 62, 62, 0.2);
            color: #feb2b2;
            border-color: rgba(229, 62, 62, 0.3);
        }
        
        .alert-info {
            background: rgba(99, 179, 237, 0.2);
            color: #bee3f8;
            border-color: rgba(99, 179, 237, 0.3);
        }
        
        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        /* RBAC Specific Styles */
        .rbac-subtab {
            display: none;
        }

        .rbac-subtab.active {
            display: block;
        }

        .rbac-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .rbac-table th,
        .rbac-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #4a5568;
        }

        .rbac-table th {
            background-color: #2d3748;
            font-weight: 600;
        }

        .rbac-table tr:hover {
            background-color: #2d3748;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background-color: #38a169;
            color: white;
        }

        .status-inactive {
            background-color: #e53e3e;
            color: white;
        }

        .status-expired {
            background-color: #d69e2e;
            color: white;
        }

        .action-buttons {
            display: flex;
            gap: 5px;
        }

        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 4px;
        }

        .user-card {
            background-color: #2d3748;
            border: 1px solid #4a5568;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
        }

        .user-card h5 {
            margin: 0 0 10px 0;
            color: #e2e8f0;
        }

        .user-meta {
            font-size: 14px;
            color: #a0aec0;
            margin-bottom: 10px;
        }

        .role-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-bottom: 10px;
        }

        .role-tag {
            background-color: #4a5568;
            color: #e2e8f0;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }

        .permission-list {
            max-height: 200px;
            overflow-y: auto;
            background-color: #2d3748;
            border: 1px solid #4a5568;
            border-radius: 6px;
            padding: 10px;
        }

        .permission-item {
            padding: 5px 0;
            border-bottom: 1px solid #4a5568;
        }

        .permission-item:last-child {
            border-bottom: none;
        }

        @media (max-width: 768px) {
            .nav-tabs {
                flex-direction: column;
            }

            .grid-2 {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <h1>🔐 Auth Service Admin Dashboard</h1>
            </div>
            <div>
                <button class="btn btn-secondary" onclick="performHealthCheck()" style="margin-right: 10px;">📊 Health Check</button>
                <button class="btn btn-logout" onclick="logout()">🚪 Logout</button>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Navigation Tabs -->
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('dashboard')">Dashboard</button>
            <button class="nav-tab" onclick="showTab('tokens')">Token Management</button>
            <button class="nav-tab" onclick="showTab('users')">User Management</button>
            <button class="nav-tab" onclick="showTab('rbac')">RBAC Management</button>
            <button class="nav-tab" onclick="showTab('organizations')">Organizations</button>
            <button class="nav-tab" onclick="showTab('security')">Security</button>
            <button class="nav-tab" onclick="showTab('system')">System</button>
        </div>

        <!-- Dashboard Tab -->
        <div id="dashboard" class="tab-content active">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="total-users">-</div>
                    <div class="stat-label">Total Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="active-tokens">-</div>
                    <div class="stat-label">Active Tokens</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="failed-logins">-</div>
                    <div class="stat-label">Failed Logins (24h)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="system-uptime">-</div>
                    <div class="stat-label">System Status</div>
                </div>
            </div>

            <div class="card">
                <h3>🚀 Quick Actions</h3>
                <button class="btn btn-primary" onclick="testOAuthFlow()">Test OAuth Flow</button>
                <button class="btn btn-secondary" onclick="refreshSystemStats()">Refresh Stats</button>
                <button class="btn btn-secondary" onclick="viewSystemLogs()">View Logs</button>
                <button class="btn btn-secondary" onclick="exportAuditLog()">Export Audit Log</button>
            </div>

            <div class="card">
                <h3>📊 System Health</h3>
                <div id="health-status">Loading system health...</div>
            </div>
        </div>

        <!-- Token Management Tab -->
        <div id="tokens" class="tab-content">
            <div class="card">
                <h3>🔑 Token Management</h3>
                <div class="alert alert-info">
                    <strong>Current Session:</strong> Manage your OAuth 2.0 tokens and test authentication flows.
                </div>
                
                <div class="grid-2">
                    <div>
                        <h4>Token Operations</h4>
                        <button class="btn btn-primary" onclick="generateTestToken()">Generate Test Token</button>
                        <button class="btn btn-secondary" onclick="validateCurrentToken()">Validate Token</button>
                        <button class="btn btn-secondary" onclick="refreshCurrentToken()">Refresh Token</button>
                        <button class="btn btn-secondary" onclick="revokeCurrentToken()">Revoke Token</button>
                    </div>
                    <div>
                        <h4>Token Analytics</h4>
                        <button class="btn btn-secondary" onclick="viewTokenStats()">Token Statistics</button>
                        <button class="btn btn-secondary" onclick="viewActiveTokens()">Active Tokens</button>
                        <button class="btn btn-secondary" onclick="viewExpiredTokens()">Expired Tokens</button>
                    </div>
                </div>

                <div id="current-token-display">
                    <h4>Current Access Token</h4>
                    <div class="token-display" id="token-content">No token generated yet</div>
                    <div id="token-info"></div>
                </div>
            </div>
        </div>

        <!-- User Management Tab -->
        <div id="users" class="tab-content">
            <div class="card">
                <h3>👥 User Management</h3>

                <div class="grid-2">
                    <div>
                        <h4>User Operations</h4>
                        <button class="btn btn-primary" onclick="loadUsers()">🔄 Refresh Users</button>
                        <button class="btn btn-secondary" onclick="showCreateUserForm()">➕ Create User</button>
                        <button class="btn btn-secondary" onclick="exportUserList()">📊 Export Users</button>
                    </div>
                    <div>
                        <h4>User Search</h4>
                        <input type="text" id="user-search" placeholder="Search users..." onkeyup="filterUsers()" style="width: 100%; padding: 8px; margin-bottom: 10px;">
                        <select id="user-filter" onchange="filterUsers()" style="width: 100%; padding: 8px;">
                            <option value="">All Users</option>
                            <option value="admin">Admins Only</option>
                            <option value="active">Active Users</option>
                            <option value="inactive">Inactive Users</option>
                        </select>
                    </div>
                </div>

                <div id="users-list">
                    <h4>Users List</h4>
                    <div id="users-table">Loading users...</div>
                </div>

                <!-- Create User Form (Hidden by default) -->
                <div id="create-user-form" style="display: none; margin-top: 20px; padding: 20px; border: 1px solid #4a5568; border-radius: 8px;">
                    <h4>Create New User</h4>
                    <div class="grid-2">
                        <div>
                            <label>Username:</label>
                            <input type="text" id="new-username" placeholder="Enter username" style="width: 100%; padding: 8px; margin-bottom: 10px;">
                            <label>Email:</label>
                            <input type="email" id="new-email" placeholder="Enter email" style="width: 100%; padding: 8px; margin-bottom: 10px;">
                            <label>First Name:</label>
                            <input type="text" id="new-firstname" placeholder="Enter first name" style="width: 100%; padding: 8px; margin-bottom: 10px;">
                        </div>
                        <div>
                            <label>Last Name:</label>
                            <input type="text" id="new-lastname" placeholder="Enter last name" style="width: 100%; padding: 8px; margin-bottom: 10px;">
                            <label>Password:</label>
                            <input type="password" id="new-password" placeholder="Enter password" style="width: 100%; padding: 8px; margin-bottom: 10px;">
                            <label>Confirm Password:</label>
                            <input type="password" id="new-password-confirm" placeholder="Confirm password" style="width: 100%; padding: 8px; margin-bottom: 10px;">
                        </div>
                    </div>
                    <div style="margin-top: 15px;">
                        <button class="btn btn-primary" onclick="createUser()">Create User</button>
                        <button class="btn btn-secondary" onclick="hideCreateUserForm()">Cancel</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- RBAC Management Tab -->
        <div id="rbac" class="tab-content">
            <div class="card">
                <h3>🔐 RBAC Management</h3>

                <div class="nav-tabs" style="margin-bottom: 20px;">
                    <button class="nav-tab active" onclick="showRBACSubTab('roles')">Roles</button>
                    <button class="nav-tab" onclick="showRBACSubTab('permissions')">Permissions</button>
                    <button class="nav-tab" onclick="showRBACSubTab('assignments')">User Assignments</button>
                    <button class="nav-tab" onclick="showRBACSubTab('projects')">Projects</button>
                </div>

                <!-- Roles Sub-tab -->
                <div id="rbac-roles" class="rbac-subtab active">
                    <h4>🎭 Role Management</h4>
                    <div class="grid-2">
                        <div>
                            <button class="btn btn-primary" onclick="loadRoles()">🔄 Refresh Roles</button>
                            <button class="btn btn-secondary" onclick="showCreateRoleForm()">➕ Create Role</button>
                        </div>
                        <div>
                            <input type="text" id="role-search" placeholder="Search roles..." onkeyup="filterRoles()" style="width: 100%; padding: 8px;">
                        </div>
                    </div>
                    <div id="roles-list" style="margin-top: 20px;">Loading roles...</div>

                    <!-- Create Role Form -->
                    <div id="create-role-form" style="display: none; margin-top: 20px; padding: 20px; border: 1px solid #4a5568; border-radius: 8px;">
                        <h5>Create New Role</h5>
                        <label>Role Name:</label>
                        <input type="text" id="new-role-name" placeholder="Enter role name" style="width: 100%; padding: 8px; margin-bottom: 10px;">
                        <label>Description:</label>
                        <textarea id="new-role-description" placeholder="Enter role description" style="width: 100%; padding: 8px; margin-bottom: 10px; height: 60px;"></textarea>
                        <label>
                            <input type="checkbox" id="new-role-system"> System Role
                        </label>
                        <div style="margin-top: 15px;">
                            <button class="btn btn-primary" onclick="createRole()">Create Role</button>
                            <button class="btn btn-secondary" onclick="hideCreateRoleForm()">Cancel</button>
                        </div>
                    </div>
                </div>

                <!-- Permissions Sub-tab -->
                <div id="rbac-permissions" class="rbac-subtab">
                    <h4>🔑 Permission Management</h4>
                    <div class="grid-2">
                        <div>
                            <button class="btn btn-primary" onclick="loadPermissions()">🔄 Refresh Permissions</button>
                            <button class="btn btn-secondary" onclick="showCreatePermissionForm()">➕ Create Permission</button>
                        </div>
                        <div>
                            <input type="text" id="permission-search" placeholder="Search permissions..." onkeyup="filterPermissions()" style="width: 100%; padding: 8px;">
                        </div>
                    </div>
                    <div id="permissions-list" style="margin-top: 20px;">Loading permissions...</div>

                    <!-- Create Permission Form -->
                    <div id="create-permission-form" style="display: none; margin-top: 20px; padding: 20px; border: 1px solid #4a5568; border-radius: 8px;">
                        <h5>Create New Permission</h5>
                        <label>Permission Name:</label>
                        <input type="text" id="new-permission-name" placeholder="Enter permission name" style="width: 100%; padding: 8px; margin-bottom: 10px;">
                        <label>Description:</label>
                        <textarea id="new-permission-description" placeholder="Enter permission description" style="width: 100%; padding: 8px; margin-bottom: 10px; height: 60px;"></textarea>
                        <div style="margin-top: 15px;">
                            <button class="btn btn-primary" onclick="createPermission()">Create Permission</button>
                            <button class="btn btn-secondary" onclick="hideCreatePermissionForm()">Cancel</button>
                        </div>
                    </div>
                </div>

                <!-- User Assignments Sub-tab -->
                <div id="rbac-assignments" class="rbac-subtab">
                    <h4>👤 User Role Assignments</h4>
                    <div class="grid-2">
                        <div>
                            <button class="btn btn-primary" onclick="loadUserAssignments()">🔄 Refresh Assignments</button>
                            <button class="btn btn-secondary" onclick="showAssignRoleForm()">➕ Assign Role</button>
                        </div>
                        <div>
                            <select id="assignment-filter" onchange="filterAssignments()" style="width: 100%; padding: 8px;">
                                <option value="">All Assignments</option>
                                <option value="active">Active Only</option>
                                <option value="expired">Expired Only</option>
                            </select>
                        </div>
                    </div>
                    <div id="assignments-list" style="margin-top: 20px;">Loading assignments...</div>

                    <!-- Assign Role Form -->
                    <div id="assign-role-form" style="display: none; margin-top: 20px; padding: 20px; border: 1px solid #4a5568; border-radius: 8px;">
                        <h5>Assign Role to User</h5>
                        <div class="grid-2">
                            <div>
                                <label>User:</label>
                                <select id="assign-user" style="width: 100%; padding: 8px; margin-bottom: 10px;">
                                    <option value="">Select User</option>
                                </select>
                                <label>Role:</label>
                                <select id="assign-role" style="width: 100%; padding: 8px; margin-bottom: 10px;">
                                    <option value="">Select Role</option>
                                </select>
                            </div>
                            <div>
                                <label>Project:</label>
                                <select id="assign-project" style="width: 100%; padding: 8px; margin-bottom: 10px;">
                                    <option value="">Select Project</option>
                                </select>
                                <label>Expires At (Optional):</label>
                                <input type="datetime-local" id="assign-expires" style="width: 100%; padding: 8px; margin-bottom: 10px;">
                            </div>
                        </div>
                        <div style="margin-top: 15px;">
                            <button class="btn btn-primary" onclick="assignUserRole()">Assign Role</button>
                            <button class="btn btn-secondary" onclick="hideAssignRoleForm()">Cancel</button>
                        </div>
                    </div>
                </div>

                <!-- Projects Sub-tab -->
                <div id="rbac-projects" class="rbac-subtab">
                    <h4>📁 Project Management</h4>
                    <div class="grid-2">
                        <div>
                            <button class="btn btn-primary" onclick="loadProjects()">🔄 Refresh Projects</button>
                            <button class="btn btn-secondary" onclick="showCreateProjectForm()">➕ Create Project</button>
                        </div>
                        <div>
                            <input type="text" id="project-search" placeholder="Search projects..." onkeyup="filterProjects()" style="width: 100%; padding: 8px;">
                        </div>
                    </div>
                    <div id="projects-list" style="margin-top: 20px;">Loading projects...</div>

                    <!-- Create Project Form -->
                    <div id="create-project-form" style="display: none; margin-top: 20px; padding: 20px; border: 1px solid #4a5568; border-radius: 8px;">
                        <h5>Create New Project</h5>
                        <div class="grid-2">
                            <div>
                                <label>Project Name:</label>
                                <input type="text" id="new-project-name" placeholder="Enter project name" style="width: 100%; padding: 8px; margin-bottom: 10px;">
                                <label>Organization:</label>
                                <select id="new-project-org" style="width: 100%; padding: 8px; margin-bottom: 10px;">
                                    <option value="">Select Organization</option>
                                </select>
                            </div>
                            <div>
                                <label>Description:</label>
                                <textarea id="new-project-description" placeholder="Enter project description" style="width: 100%; padding: 8px; margin-bottom: 10px; height: 80px;"></textarea>
                            </div>
                        </div>
                        <div style="margin-top: 15px;">
                            <button class="btn btn-primary" onclick="createProject()">Create Project</button>
                            <button class="btn btn-secondary" onclick="hideCreateProjectForm()">Cancel</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Organizations Tab -->
        <div id="organizations" class="tab-content">
            <div class="card">
                <h3>🏢 Organization Management</h3>

                <div class="grid-2">
                    <div>
                        <button class="btn btn-primary" onclick="loadOrganizations()">🔄 Refresh Organizations</button>
                        <button class="btn btn-secondary" onclick="showCreateOrgForm()">➕ Create Organization</button>
                    </div>
                    <div>
                        <input type="text" id="org-search" placeholder="Search organizations..." onkeyup="filterOrganizations()" style="width: 100%; padding: 8px;">
                    </div>
                </div>

                <div id="organizations-list" style="margin-top: 20px;">Loading organizations...</div>

                <!-- Create Organization Form -->
                <div id="create-org-form" style="display: none; margin-top: 20px; padding: 20px; border: 1px solid #4a5568; border-radius: 8px;">
                    <h4>Create New Organization</h4>
                    <div class="grid-2">
                        <div>
                            <label>Organization Name:</label>
                            <input type="text" id="new-org-name" placeholder="Enter organization name" style="width: 100%; padding: 8px; margin-bottom: 10px;">
                            <label>Domain (Optional):</label>
                            <input type="text" id="new-org-domain" placeholder="Enter domain (e.g., company.com)" style="width: 100%; padding: 8px; margin-bottom: 10px;">
                        </div>
                        <div>
                            <label>Description:</label>
                            <textarea id="new-org-description" placeholder="Enter organization description" style="width: 100%; padding: 8px; margin-bottom: 10px; height: 80px;"></textarea>
                        </div>
                    </div>
                    <div style="margin-top: 15px;">
                        <button class="btn btn-primary" onclick="createOrganization()">Create Organization</button>
                        <button class="btn btn-secondary" onclick="hideCreateOrgForm()">Cancel</button>
                    </div>
                </div>
            </div>
        </div>

        <div id="security" class="tab-content">
            <div class="card">
                <h3>🛡️ Security Management</h3>
                <p>Security management functionality will be implemented here.</p>
            </div>
        </div>

        <div id="system" class="tab-content">
            <div class="card">
                <h3>⚙️ System Administration</h3>
                <p>System administration functionality will be implemented here.</p>
            </div>
        </div>
    </div>

    <!-- Message Display -->
    <div id="message-container" style="position: fixed; top: 20px; right: 20px; z-index: 1000;"></div>

    <script>
        // Global variables
        let currentToken = null;
        let refreshToken = null;

        // API Configuration - Use port 8082 for auth-service API calls
        const API_BASE_URL = window.location.hostname === 'localhost' ?
            'http://localhost:8082' :
            `http://${window.location.hostname}:8082`;

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            checkAdminAccess();
            loadSystemHealth();
            loadSystemStats();
            loadSystemInfo();
            loadUsers();
            loadRoles();
            loadPermissions();
            loadOrganizations();
            loadProjects();
        });

        // Check if user has admin access
        function checkAdminAccess() {
            const userInfo = JSON.parse(localStorage.getItem('user_info') || '{}');
            const token = localStorage.getItem('auth_token');

            if (!token) {
                alert('No authentication token found. Redirecting to login...');
                window.location.href = '/';
                return;
            }

            // Check if user is admin (since OAuth response doesn't include is_admin flag)
            if (userInfo.username !== 'btaylor-admin') {
                alert('Access denied. Admin privileges required.');
                window.location.href = '/dashboard.html';
                return;
            }

            // Update header with user info
            if (userInfo.username) {
                const headerTitle = document.querySelector('.header h1');
                headerTitle.innerHTML = `🔐 Auth Service Admin Dashboard - ${userInfo.username}`;
            }
        }

        // Tab management
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // Remove active class from all nav tabs
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked nav tab
            event.target.classList.add('active');
        }

        // RBAC Sub-tab management
        function showRBACSubTab(subTabName) {
            // Hide all RBAC sub-tabs
            document.querySelectorAll('.rbac-subtab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Remove active class from all RBAC nav tabs
            document.querySelectorAll('#rbac .nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected sub-tab
            document.getElementById('rbac-' + subTabName).classList.add('active');

            // Add active class to clicked nav tab
            event.target.classList.add('active');
        }

        // Message display
        function showMessage(message, type = 'info') {
            const container = document.getElementById('message-container');
            const messageDiv = document.createElement('div');
            messageDiv.className = `alert alert-${type}`;
            messageDiv.style.marginBottom = '10px';
            messageDiv.textContent = message;

            container.appendChild(messageDiv);

            setTimeout(() => {
                container.removeChild(messageDiv);
            }, 5000);
        }

        // System health and stats
        async function loadSystemHealth() {
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                const data = await response.json();

                document.getElementById('health-status').innerHTML = `
                    <p><strong>Status:</strong> <span style="color: #68d391;">${data.status}</span></p>
                    <p><strong>Service:</strong> ${data.service}</p>
                    <p><strong>Version:</strong> ${data.version}</p>
                    <p><strong>Timestamp:</strong> ${new Date(data.timestamp * 1000).toLocaleString()}</p>
                    <p><strong>OAuth Endpoints:</strong> ${data.oauth2_endpoints.join(', ')}</p>
                `;

                document.getElementById('system-uptime').textContent = 'Healthy';

            } catch (error) {
                document.getElementById('health-status').innerHTML = `
                    <p><strong>Status:</strong> <span style="color: #fc8181;">Error loading health data</span></p>
                `;
                console.error('Health check failed:', error);
            }
        }

        function loadSystemStats() {
            // Mock stats - in real implementation, these would come from API
            document.getElementById('total-users').textContent = '2';
            document.getElementById('active-tokens').textContent = currentToken ? '1' : '0';
            document.getElementById('failed-logins').textContent = '0';
        }

        function loadSystemInfo() {
            // Mock system info
            console.log('System info loaded');
        }

        // Token management functions
        async function generateTestToken() {
            try {
                // Use current user's token or generate new one
                const userInfo = JSON.parse(localStorage.getItem('user_info') || '{}');
                const storedToken = localStorage.getItem('auth_token');

                if (storedToken) {
                    // Use existing token
                    currentToken = storedToken;
                    refreshToken = localStorage.getItem('refresh_token') || '';

                    document.getElementById('token-content').textContent = currentToken;
                    document.getElementById('token-info').innerHTML = `
                        <p><strong>Token Type:</strong> Bearer</p>
                        <p><strong>User:</strong> ${userInfo.username}</p>
                        <p><strong>Login Time:</strong> ${new Date(userInfo.login_time).toLocaleString()}</p>
                        <p><strong>Admin:</strong> ${userInfo.username === 'btaylor-admin' ? 'Yes' : 'No'}</p>
                    `;

                    loadSystemStats();
                    showMessage('Current token loaded successfully!', 'success');
                } else {
                    showMessage('No token available. Please login first.', 'error');
                }
            } catch (error) {
                showMessage('Error during token operation: ' + error.message, 'error');
            }
        }

        async function validateCurrentToken() {
            if (!currentToken) {
                showMessage('No token to validate. Generate a token first.', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/oauth/validate`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ token: currentToken })
                });

                if (response.ok) {
                    const data = await response.json();
                    showMessage(`Token is valid! User: ${data.user_id || 'Unknown'}`, 'success');
                } else {
                    showMessage('Token validation failed', 'error');
                }
            } catch (error) {
                showMessage('Error validating token', 'error');
            }
        }

        async function refreshCurrentToken() {
            if (!refreshToken) {
                showMessage('No refresh token available.', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/oauth/refresh`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ refresh_token: refreshToken })
                });

                if (response.ok) {
                    const data = await response.json();
                    currentToken = data.access_token;

                    document.getElementById('token-content').textContent = data.access_token;
                    showMessage('Token refreshed successfully!', 'success');
                } else {
                    showMessage('Token refresh failed', 'error');
                }
            } catch (error) {
                showMessage('Error refreshing token', 'error');
            }
        }

        async function revokeCurrentToken() {
            if (!currentToken) {
                showMessage('No token to revoke.', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/oauth/revoke`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ token: currentToken })
                });

                if (response.ok) {
                    currentToken = null;
                    refreshToken = null;
                    document.getElementById('token-content').textContent = 'No token generated yet';
                    document.getElementById('token-info').innerHTML = '';
                    loadSystemStats();
                    showMessage('Token revoked successfully!', 'success');
                } else {
                    showMessage('Token revocation failed', 'error');
                }
            } catch (error) {
                showMessage('Error revoking token', 'error');
            }
        }

        // Dashboard functions
        function testOAuthFlow() {
            showMessage('Testing complete OAuth 2.0 flow...', 'info');
            generateTestToken();
        }

        function refreshSystemStats() {
            loadSystemHealth();
            loadSystemStats();
            loadSystemInfo();
            showMessage('System statistics refreshed!', 'success');
        }

        // Header functions
        async function performHealthCheck() {
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                const data = await response.json();
                showMessage(`Service Status: ${data.status} (v${data.version}) - ${data.oauth2_endpoints.length} endpoints available`, 'success');
                console.log('Health Check:', data);
            } catch (error) {
                showMessage('Health check failed: Service unavailable', 'error');
                console.error('Health check error:', error);
            }
        }

        function logout() {
            // Clear any stored tokens
            currentToken = null;
            refreshToken = null;
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user_info');

            // Redirect to main page immediately
            window.location.href = '/';
        }

        // User Management Functions
        async function loadUsers() {
            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch('/api/admin/users', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                if (response.ok) {
                    const users = await response.json();
                    displayUsers(users);
                } else {
                    console.log('API endpoint not available, using mock data');
                    // Mock data for demonstration when API is not available
                    const mockUsers = [
                        {
                            user_id: '123e4567-e89b-12d3-a456-426614174000',
                            username: 'testuser',
                            email: '<EMAIL>',
                            first_name: 'Test',
                            last_name: 'User',
                            is_active: true,
                            created_at: new Date().toISOString()
                        },
                        {
                            user_id: '123e4567-e89b-12d3-a456-426614174001',
                            username: 'btaylor-admin',
                            email: '<EMAIL>',
                            first_name: 'Brian',
                            last_name: 'Taylor',
                            is_active: true,
                            created_at: new Date().toISOString()
                        }
                    ];
                    displayUsers(mockUsers);
                    showMessage('Note: Using demo data. User management API endpoints are not yet implemented.', 'info');
                }
            } catch (error) {
                console.log('Network error, using mock data');
                // Fallback to mock data on network error
                const mockUsers = [
                    {
                        user_id: '123e4567-e89b-12d3-a456-426614174000',
                        username: 'testuser',
                        email: '<EMAIL>',
                        first_name: 'Test',
                        last_name: 'User',
                        is_active: true,
                        created_at: new Date().toISOString()
                    },
                    {
                        user_id: '123e4567-e89b-12d3-a456-426614174001',
                        username: 'btaylor-admin',
                        email: '<EMAIL>',
                        first_name: 'Brian',
                        last_name: 'Taylor',
                        is_active: true,
                        created_at: new Date().toISOString()
                    }
                ];
                displayUsers(mockUsers);
                showMessage('Note: Using demo data. Network error or server unavailable.', 'info');
            }
        }

        function displayUsers(users) {
            const container = document.getElementById('users-table');
            if (!users || users.length === 0) {
                container.innerHTML = '<p>No users found.</p>';
                return;
            }

            let html = '<table class="rbac-table"><thead><tr>';
            html += '<th>Username</th><th>Email</th><th>Name</th><th>Status</th><th>Created</th><th>Actions</th>';
            html += '</tr></thead><tbody>';

            users.forEach(user => {
                const isAdmin = user.username === 'btaylor-admin';
                const statusClass = user.is_active ? 'status-active' : 'status-inactive';
                const statusText = user.is_active ? 'Active' : 'Inactive';

                html += `<tr>
                    <td>${user.username} ${isAdmin ? '👑' : ''}</td>
                    <td>${user.email || 'N/A'}</td>
                    <td>${user.first_name || ''} ${user.last_name || ''}</td>
                    <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                    <td>${new Date(user.created_at).toLocaleDateString()}</td>
                    <td class="action-buttons">
                        <button class="btn btn-small btn-secondary" onclick="viewUserRoles('${user.user_id}')">Roles</button>
                        <button class="btn btn-small btn-secondary" onclick="viewUserPermissions('${user.user_id}')">Permissions</button>
                        <button class="btn btn-small btn-secondary" onclick="editUser('${user.user_id}')">Edit</button>
                    </td>
                </tr>`;
            });

            html += '</tbody></table>';
            container.innerHTML = html;
        }

        function showCreateUserForm() {
            document.getElementById('create-user-form').style.display = 'block';
        }

        function hideCreateUserForm() {
            document.getElementById('create-user-form').style.display = 'none';
            // Clear form
            document.getElementById('new-username').value = '';
            document.getElementById('new-email').value = '';
            document.getElementById('new-firstname').value = '';
            document.getElementById('new-lastname').value = '';
            document.getElementById('new-password').value = '';
            document.getElementById('new-password-confirm').value = '';
        }

        async function createUser() {
            const username = document.getElementById('new-username').value;
            const email = document.getElementById('new-email').value;
            const firstName = document.getElementById('new-firstname').value;
            const lastName = document.getElementById('new-lastname').value;
            const password = document.getElementById('new-password').value;
            const confirmPassword = document.getElementById('new-password-confirm').value;

            if (!username || !password) {
                showMessage('Username and password are required', 'error');
                return;
            }

            if (password !== confirmPassword) {
                showMessage('Passwords do not match', 'error');
                return;
            }

            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch('/api/admin/users', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        username,
                        email,
                        first_name: firstName,
                        last_name: lastName,
                        password
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    showMessage('User created successfully!', 'success');
                    hideCreateUserForm();
                    loadUsers();
                } else {
                    // Try to parse error response
                    let errorMessage = 'Unknown error';
                    try {
                        const error = await response.json();
                        errorMessage = error.message || error.error_description || 'Server returned an error';
                    } catch (parseError) {
                        // If response is not JSON, show the status
                        errorMessage = `Server error (${response.status}): ${response.statusText}`;
                    }
                    showMessage('Error creating user: ' + errorMessage, 'error');
                }
            } catch (error) {
                showMessage('Error creating user: Network error or server unavailable', 'error');
                console.error('Create user error:', error);
            }
        }

        function filterUsers() {
            const searchTerm = document.getElementById('user-search').value.toLowerCase();
            const filterType = document.getElementById('user-filter').value;

            // This would filter the displayed users based on search term and filter type
            // For now, just reload users
            loadUsers();
        }

        function exportUserList() {
            showMessage('User export feature coming soon!', 'info');
        }

        // RBAC Management Functions
        async function loadRoles() {
            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch(`${API_BASE_URL}/api/admin/roles`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                if (response.ok) {
                    const roles = await response.json();
                    displayRoles(roles);
                } else {
                    // Mock data for demonstration
                    const mockRoles = [
                        {
                            role_id: '123e4567-e89b-12d3-a456-426614174100',
                            role_name: 'admin',
                            description: 'Full system administrator access',
                            is_system_role: true,
                            created_at: new Date().toISOString()
                        },
                        {
                            role_id: '123e4567-e89b-12d3-a456-426614174101',
                            role_name: 'user',
                            description: 'Standard user access',
                            is_system_role: false,
                            created_at: new Date().toISOString()
                        }
                    ];
                    displayRoles(mockRoles);
                }
            } catch (error) {
                showMessage('Error loading roles: ' + error.message, 'error');
            }
        }

        function displayRoles(roles) {
            const container = document.getElementById('roles-list');
            if (!roles || roles.length === 0) {
                container.innerHTML = '<p>No roles found.</p>';
                return;
            }

            let html = '<table class="rbac-table"><thead><tr>';
            html += '<th>Role Name</th><th>Description</th><th>Type</th><th>Created</th><th>Actions</th>';
            html += '</tr></thead><tbody>';

            roles.forEach(role => {
                const roleType = role.is_system_role ? 'System' : 'Custom';
                const typeClass = role.is_system_role ? 'status-active' : 'status-inactive';

                html += `<tr>
                    <td>${role.role_name}</td>
                    <td>${role.description || 'No description'}</td>
                    <td><span class="status-badge ${typeClass}">${roleType}</span></td>
                    <td>${new Date(role.created_at).toLocaleDateString()}</td>
                    <td class="action-buttons">
                        <button class="btn btn-small btn-secondary" onclick="viewRolePermissions('${role.role_id}')">Permissions</button>
                        <button class="btn btn-small btn-secondary" onclick="editRole('${role.role_id}')">Edit</button>
                        ${!role.is_system_role ? `<button class="btn btn-small btn-secondary" onclick="deleteRole('${role.role_id}')">Delete</button>` : ''}
                    </td>
                </tr>`;
            });

            html += '</tbody></table>';
            container.innerHTML = html;
        }

        function showCreateRoleForm() {
            document.getElementById('create-role-form').style.display = 'block';
        }

        function hideCreateRoleForm() {
            document.getElementById('create-role-form').style.display = 'none';
            document.getElementById('new-role-name').value = '';
            document.getElementById('new-role-description').value = '';
            document.getElementById('new-role-system').checked = false;
        }

        async function createRole() {
            const roleName = document.getElementById('new-role-name').value;
            const description = document.getElementById('new-role-description').value;
            const isSystemRole = document.getElementById('new-role-system').checked;

            if (!roleName) {
                showMessage('Role name is required', 'error');
                return;
            }

            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch(`${API_BASE_URL}/api/admin/roles`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        role_name: roleName,
                        description,
                        is_system_role: isSystemRole
                    })
                });

                if (response.ok) {
                    showMessage('Role created successfully!', 'success');
                    hideCreateRoleForm();
                    loadRoles();
                } else {
                    const error = await response.json();
                    showMessage('Error creating role: ' + (error.message || 'Unknown error'), 'error');
                }
            } catch (error) {
                showMessage('Error creating role: ' + error.message, 'error');
            }
        }

        async function loadPermissions() {
            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch(`${API_BASE_URL}/api/admin/permissions`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                if (response.ok) {
                    const permissions = await response.json();
                    displayPermissions(permissions);
                } else {
                    // Mock data for demonstration
                    const mockPermissions = [
                        {
                            permission_id: '123e4567-e89b-12d3-a456-426614174200',
                            permission_name: 'user.create',
                            description: 'Create new users',
                            created_at: new Date().toISOString()
                        },
                        {
                            permission_id: '123e4567-e89b-12d3-a456-426614174201',
                            permission_name: 'user.read',
                            description: 'View user information',
                            created_at: new Date().toISOString()
                        },
                        {
                            permission_id: '123e4567-e89b-12d3-a456-426614174202',
                            permission_name: 'user.update',
                            description: 'Update user information',
                            created_at: new Date().toISOString()
                        },
                        {
                            permission_id: '123e4567-e89b-12d3-a456-426614174203',
                            permission_name: 'user.delete',
                            description: 'Delete users',
                            created_at: new Date().toISOString()
                        }
                    ];
                    displayPermissions(mockPermissions);
                }
            } catch (error) {
                showMessage('Error loading permissions: ' + error.message, 'error');
            }
        }

        function displayPermissions(permissions) {
            const container = document.getElementById('permissions-list');
            if (!permissions || permissions.length === 0) {
                container.innerHTML = '<p>No permissions found.</p>';
                return;
            }

            let html = '<table class="rbac-table"><thead><tr>';
            html += '<th>Permission Name</th><th>Description</th><th>Created</th><th>Actions</th>';
            html += '</tr></thead><tbody>';

            permissions.forEach(permission => {
                html += `<tr>
                    <td><code>${permission.permission_name}</code></td>
                    <td>${permission.description || 'No description'}</td>
                    <td>${new Date(permission.created_at).toLocaleDateString()}</td>
                    <td class="action-buttons">
                        <button class="btn btn-small btn-secondary" onclick="editPermission('${permission.permission_id}')">Edit</button>
                        <button class="btn btn-small btn-secondary" onclick="deletePermission('${permission.permission_id}')">Delete</button>
                    </td>
                </tr>`;
            });

            html += '</tbody></table>';
            container.innerHTML = html;
        }

        // Organization Management Functions
        async function loadOrganizations() {
            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch('/api/organizations', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                if (response.ok) {
                    const organizations = await response.json();
                    displayOrganizations(organizations);
                } else {
                    // Mock data for demonstration
                    const mockOrganizations = [
                        {
                            org_id: '123e4567-e89b-12d3-a456-426614174300',
                            org_name: 'Default Organization',
                            org_domain: 'chcit.org',
                            is_active: true,
                            created_at: new Date().toISOString()
                        }
                    ];
                    displayOrganizations(mockOrganizations);
                }
            } catch (error) {
                showMessage('Error loading organizations: ' + error.message, 'error');
            }
        }

        function displayOrganizations(organizations) {
            const container = document.getElementById('organizations-list');
            if (!organizations || organizations.length === 0) {
                container.innerHTML = '<p>No organizations found.</p>';
                return;
            }

            let html = '<table class="rbac-table"><thead><tr>';
            html += '<th>Organization Name</th><th>Domain</th><th>Status</th><th>Created</th><th>Actions</th>';
            html += '</tr></thead><tbody>';

            organizations.forEach(org => {
                const statusClass = org.is_active ? 'status-active' : 'status-inactive';
                const statusText = org.is_active ? 'Active' : 'Inactive';

                html += `<tr>
                    <td>${org.org_name}</td>
                    <td>${org.org_domain || 'N/A'}</td>
                    <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                    <td>${new Date(org.created_at).toLocaleDateString()}</td>
                    <td class="action-buttons">
                        <button class="btn btn-small btn-secondary" onclick="viewOrgProjects('${org.org_id}')">Projects</button>
                        <button class="btn btn-small btn-secondary" onclick="editOrganization('${org.org_id}')">Edit</button>
                    </td>
                </tr>`;
            });

            html += '</tbody></table>';
            container.innerHTML = html;
        }

        async function loadProjects() {
            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch('/api/projects', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                if (response.ok) {
                    const projects = await response.json();
                    displayProjects(projects);
                } else {
                    // Mock data for demonstration
                    const mockProjects = [
                        {
                            project_id: '123e4567-e89b-12d3-a456-426614174400',
                            project_name: 'Default Project',
                            org_name: 'Default Organization',
                            description: 'Default project for the organization',
                            is_active: true,
                            created_at: new Date().toISOString()
                        }
                    ];
                    displayProjects(mockProjects);
                }
            } catch (error) {
                showMessage('Error loading projects: ' + error.message, 'error');
            }
        }

        function displayProjects(projects) {
            const container = document.getElementById('projects-list');
            if (!projects || projects.length === 0) {
                container.innerHTML = '<p>No projects found.</p>';
                return;
            }

            let html = '<table class="rbac-table"><thead><tr>';
            html += '<th>Project Name</th><th>Organization</th><th>Description</th><th>Status</th><th>Created</th><th>Actions</th>';
            html += '</tr></thead><tbody>';

            projects.forEach(project => {
                const statusClass = project.is_active ? 'status-active' : 'status-inactive';
                const statusText = project.is_active ? 'Active' : 'Inactive';

                html += `<tr>
                    <td>${project.project_name}</td>
                    <td>${project.org_name || 'N/A'}</td>
                    <td>${project.description || 'No description'}</td>
                    <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                    <td>${new Date(project.created_at).toLocaleDateString()}</td>
                    <td class="action-buttons">
                        <button class="btn btn-small btn-secondary" onclick="viewProjectUsers('${project.project_id}')">Users</button>
                        <button class="btn btn-small btn-secondary" onclick="editProject('${project.project_id}')">Edit</button>
                    </td>
                </tr>`;
            });

            html += '</tbody></table>';
            container.innerHTML = html;
        }

        // User Assignment Functions
        async function loadUserAssignments() {
            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch('/api/admin/user-assignments', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                if (response.ok) {
                    const assignments = await response.json();
                    displayUserAssignments(assignments);
                } else {
                    // Mock data for demonstration
                    const mockAssignments = [
                        {
                            assignment_id: '123e4567-e89b-12d3-a456-426614174500',
                            username: 'btaylor-admin',
                            role_name: 'admin',
                            project_name: 'Default Project',
                            is_active: true,
                            expires_at: null,
                            created_at: new Date().toISOString()
                        },
                        {
                            assignment_id: '123e4567-e89b-12d3-a456-426614174501',
                            username: 'testuser',
                            role_name: 'user',
                            project_name: 'Default Project',
                            is_active: true,
                            expires_at: null,
                            created_at: new Date().toISOString()
                        }
                    ];
                    displayUserAssignments(mockAssignments);
                }
            } catch (error) {
                showMessage('Error loading user assignments: ' + error.message, 'error');
            }
        }

        function displayUserAssignments(assignments) {
            const container = document.getElementById('assignments-list');
            if (!assignments || assignments.length === 0) {
                container.innerHTML = '<p>No assignments found.</p>';
                return;
            }

            let html = '<table class="rbac-table"><thead><tr>';
            html += '<th>User</th><th>Role</th><th>Project</th><th>Status</th><th>Expires</th><th>Created</th><th>Actions</th>';
            html += '</tr></thead><tbody>';

            assignments.forEach(assignment => {
                let statusClass = 'status-active';
                let statusText = 'Active';

                if (!assignment.is_active) {
                    statusClass = 'status-inactive';
                    statusText = 'Inactive';
                } else if (assignment.expires_at && new Date(assignment.expires_at) < new Date()) {
                    statusClass = 'status-expired';
                    statusText = 'Expired';
                }

                const expiresText = assignment.expires_at ?
                    new Date(assignment.expires_at).toLocaleDateString() : 'Never';

                html += `<tr>
                    <td>${assignment.username}</td>
                    <td>${assignment.role_name}</td>
                    <td>${assignment.project_name}</td>
                    <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                    <td>${expiresText}</td>
                    <td>${new Date(assignment.created_at).toLocaleDateString()}</td>
                    <td class="action-buttons">
                        <button class="btn btn-small btn-secondary" onclick="editAssignment('${assignment.assignment_id}')">Edit</button>
                        <button class="btn btn-small btn-secondary" onclick="revokeAssignment('${assignment.assignment_id}')">Revoke</button>
                    </td>
                </tr>`;
            });

            html += '</tbody></table>';
            container.innerHTML = html;
        }

        // Mock functions for features not yet implemented
        function viewUserRoles(userId) { showMessage(`View roles for user ${userId} - Coming soon!`, 'info'); }
        function viewUserPermissions(userId) { showMessage(`View permissions for user ${userId} - Coming soon!`, 'info'); }
        function editUser(userId) { showMessage(`Edit user ${userId} - Coming soon!`, 'info'); }
        function viewRolePermissions(roleId) { showMessage(`View permissions for role ${roleId} - Coming soon!`, 'info'); }
        function editRole(roleId) { showMessage(`Edit role ${roleId} - Coming soon!`, 'info'); }
        function deleteRole(roleId) { showMessage(`Delete role ${roleId} - Coming soon!`, 'info'); }
        function editPermission(permissionId) { showMessage(`Edit permission ${permissionId} - Coming soon!`, 'info'); }
        function deletePermission(permissionId) { showMessage(`Delete permission ${permissionId} - Coming soon!`, 'info'); }
        function viewOrgProjects(orgId) { showMessage(`View projects for organization ${orgId} - Coming soon!`, 'info'); }
        function editOrganization(orgId) { showMessage(`Edit organization ${orgId} - Coming soon!`, 'info'); }
        function viewProjectUsers(projectId) { showMessage(`View users for project ${projectId} - Coming soon!`, 'info'); }
        function editProject(projectId) { showMessage(`Edit project ${projectId} - Coming soon!`, 'info'); }
        function editAssignment(assignmentId) { showMessage(`Edit assignment ${assignmentId} - Coming soon!`, 'info'); }
        function revokeAssignment(assignmentId) { showMessage(`Revoke assignment ${assignmentId} - Coming soon!`, 'info'); }
        function showCreatePermissionForm() {
            document.getElementById('create-permission-form').style.display = 'block';
        }

        function hideCreatePermissionForm() {
            document.getElementById('create-permission-form').style.display = 'none';
            // Clear form fields
            document.getElementById('new-permission-name').value = '';
            document.getElementById('new-permission-description').value = '';
        }

        async function createPermission() {
            const permissionName = document.getElementById('new-permission-name').value;
            const description = document.getElementById('new-permission-description').value;

            if (!permissionName) {
                showMessage('Permission name is required', 'error');
                return;
            }

            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch(`${API_BASE_URL}/api/admin/permissions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        permission_name: permissionName,
                        description: description
                    })
                });

                if (response.ok) {
                    showMessage('Permission created successfully!', 'success');
                    hideCreatePermissionForm();
                    loadPermissions();
                } else {
                    const error = await response.json();
                    showMessage('Error creating permission: ' + (error.message || 'Unknown error'), 'error');
                }
            } catch (error) {
                showMessage('Error creating permission: ' + error.message, 'error');
            }
        }
        function showAssignRoleForm() { showMessage('Assign role form - Coming soon!', 'info'); }
        function hideAssignRoleForm() { showMessage('Hide assign role form - Coming soon!', 'info'); }
        function assignUserRole() { showMessage('Assign user role - Coming soon!', 'info'); }
        function showCreateProjectForm() { showMessage('Create project form - Coming soon!', 'info'); }
        function hideCreateProjectForm() { showMessage('Hide project form - Coming soon!', 'info'); }
        function createProject() { showMessage('Create project - Coming soon!', 'info'); }
        function showCreateOrgForm() { showMessage('Create organization form - Coming soon!', 'info'); }
        function hideCreateOrgForm() { showMessage('Hide organization form - Coming soon!', 'info'); }
        function createOrganization() { showMessage('Create organization - Coming soon!', 'info'); }
        function filterRoles() { loadRoles(); }
        function filterPermissions() { loadPermissions(); }
        function filterAssignments() { loadUserAssignments(); }
        function filterProjects() { loadProjects(); }
        function filterOrganizations() { loadOrganizations(); }
        function viewSystemLogs() { showMessage('System logs viewer coming soon!', 'info'); }
        function exportAuditLog() { showMessage('Audit log export feature coming soon!', 'info'); }
        function viewTokenStats() { showMessage('Token statistics feature coming soon!', 'info'); }
        function viewActiveTokens() { showMessage('Active tokens view coming soon!', 'info'); }
        function viewExpiredTokens() { showMessage('Expired tokens view coming soon!', 'info'); }
    </script>
</body>
</html>
