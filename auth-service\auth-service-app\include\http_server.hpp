﻿#pragma once
#include <memory>
#include <string>
#include <unordered_map>
#include <functional>

// Forward declarations
class UserManager;
class JWTManager;
class RateLimiter;
class RBACManager;
class EnhancedTokenManager;

/**
 * @brief HTTP Server with OAuth 2.0 API Endpoints
 *
 * Implements OAuth 2.0 REST API endpoints:
 * - POST /oauth/token - Token generation
 * - POST /oauth/refresh - Token refresh
 * - POST /oauth/validate - Token validation
 * - POST /oauth/revoke - Token revocation
 * - GET /health - Health check
 *
 * Step 5 of OAuth 2.0 Implementation
 */
class HttpServer {
public:
    /**
     * @brief HTTP Request structure
     */
    struct HttpRequest {
        std::string method;
        std::string path;
        std::unordered_map<std::string, std::string> headers;
        std::string body;
        std::unordered_map<std::string, std::string> query_params;
    };

    /**
     * @brief HTTP Response structure
     */
    struct HttpResponse {
        int status_code;
        std::unordered_map<std::string, std::string> headers;
        std::string body;

        HttpResponse(int code = 200) : status_code(code) {
            headers["Content-Type"] = "application/json";
            headers["Server"] = "auth-service/1.0";
        }
    };

    /**
     * @brief Route handler function type
     */
    using RouteHandler = std::function<HttpResponse(const HttpRequest&)>;

public:
    /**
     * @brief Constructor
     * @param user_manager User management component
     * @param jwt_manager JWT token management component
     * @param rbac_manager RBAC management component
     * @param enhanced_token_manager Enhanced token management component
     */
    explicit HttpServer(UserManager* user_manager, JWTManager* jwt_manager,
                       RBACManager* rbac_manager, EnhancedTokenManager* enhanced_token_manager);

    /**
     * @brief Destructor
     */
    ~HttpServer();

    /**
     * @brief Start HTTP server
     * @param port Port to listen on
     */
    void start(int port);

    /**
     * @brief Stop HTTP server
     */
    void stop();

private:
    /**
     * @brief Initialize OAuth 2.0 API routes
     */
    void initializeRoutes();

    /**
     * @brief Process HTTP request
     * @param request HTTP request
     * @return HTTP response
     */
    HttpResponse processRequest(const HttpRequest& request);

    /**
     * @brief Parse HTTP request from raw data
     * @param raw_request Raw HTTP request string
     * @return Parsed HTTP request
     */
    HttpRequest parseRequest(const std::string& raw_request);

    /**
     * @brief Generate HTTP response string
     * @param response HTTP response object
     * @return Raw HTTP response string
     */
    std::string generateResponse(const HttpResponse& response);

    // OAuth 2.0 API Endpoint Handlers
    HttpResponse handleTokenRequest(const HttpRequest& request);
    HttpResponse handleRefreshRequest(const HttpRequest& request);
    HttpResponse handleValidateRequest(const HttpRequest& request);
    HttpResponse handleRevokeRequest(const HttpRequest& request);
    HttpResponse handleHealthCheck(const HttpRequest& request);

    // Enhanced RBAC API Endpoint Handlers
    HttpResponse handleProjectScopedTokenRequest(const std::string& user_id,
                                               const std::string& project_id,
                                               const std::string& scope,
                                               const HttpRequest& request);

    // RBAC Management API Endpoints
    HttpResponse handleGetUserRoles(const HttpRequest& request);
    HttpResponse handleAssignUserRole(const HttpRequest& request);
    HttpResponse handleRemoveUserRole(const HttpRequest& request);
    HttpResponse handleGetUserPermissions(const HttpRequest& request);
    HttpResponse handleGetProjectUsers(const HttpRequest& request);
    HttpResponse handleGetOrganizations(const HttpRequest& request);
    HttpResponse handleGetProjects(const HttpRequest& request);

    // Admin User Management API Endpoints
    HttpResponse handleGetAllUsers(const HttpRequest& request);
    HttpResponse handleCreateUser(const HttpRequest& request);
    HttpResponse handleUpdateUser(const HttpRequest& request);
    HttpResponse handleDeleteUser(const HttpRequest& request);

    // Admin Role Management API Endpoints
    HttpResponse handleGetAllRoles(const HttpRequest& request);
    HttpResponse handleCreateRole(const HttpRequest& request);
    HttpResponse handleUpdateRole(const HttpRequest& request);
    HttpResponse handleDeleteRole(const HttpRequest& request);

    // Admin Permission Management API Endpoints
    HttpResponse handleGetAllPermissions(const HttpRequest& request);
    HttpResponse handleCreatePermission(const HttpRequest& request);
    HttpResponse handleUpdatePermission(const HttpRequest& request);
    HttpResponse handleDeletePermission(const HttpRequest& request);

    // Admin User Assignment API Endpoints
    HttpResponse handleGetUserAssignments(const HttpRequest& request);
    HttpResponse handleCreateUserAssignment(const HttpRequest& request);
    HttpResponse handleDeleteUserAssignment(const HttpRequest& request);

    // Utility methods
    HttpResponse createErrorResponse(int status_code, const std::string& error,
                                   const std::string& description = "");
    HttpResponse createSuccessResponse(const std::string& json_body);
    std::string extractBearerToken(const HttpRequest& request);
    std::unordered_map<std::string, std::string> parseJsonBody(const std::string& body);

    // Rate limiting
    std::string extractClientIP(const HttpRequest& request);
    HttpResponse handleRateLimit(const std::string& client_ip, const std::string& endpoint);

private:
    UserManager* user_manager_;
    JWTManager* jwt_manager_;
    RBACManager* rbac_manager_;
    EnhancedTokenManager* enhanced_token_manager_;
    std::unique_ptr<RateLimiter> rate_limiter_;
    bool running_;
    int server_port_;

    // Route mapping
    std::unordered_map<std::string, RouteHandler> routes_;
};
