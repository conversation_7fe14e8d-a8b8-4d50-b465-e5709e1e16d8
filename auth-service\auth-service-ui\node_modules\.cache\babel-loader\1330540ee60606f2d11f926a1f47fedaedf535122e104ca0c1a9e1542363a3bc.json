{"ast": null, "code": "import merge from './merge';\nfunction compose(...styles) {\n  const handlers = styles.reduce((acc, style) => {\n    style.filterProps.forEach(prop => {\n      acc[prop] = style;\n    });\n    return acc;\n  }, {});\n\n  // false positive\n  // eslint-disable-next-line react/function-component-definition\n  const fn = props => {\n    return Object.keys(props).reduce((acc, prop) => {\n      if (handlers[prop]) {\n        return merge(acc, handlers[prop](props));\n      }\n      return acc;\n    }, {});\n  };\n  fn.propTypes = process.env.NODE_ENV !== 'production' ? styles.reduce((acc, style) => Object.assign(acc, style.propTypes), {}) : {};\n  fn.filterProps = styles.reduce((acc, style) => acc.concat(style.filterProps), []);\n  return fn;\n}\nexport default compose;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}