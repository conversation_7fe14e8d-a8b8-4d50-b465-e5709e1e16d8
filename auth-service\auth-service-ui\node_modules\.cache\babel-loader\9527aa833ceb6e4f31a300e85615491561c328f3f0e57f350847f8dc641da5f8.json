{"ast": null, "code": "function r(e) {\n  var t,\n    f,\n    n = \"\";\n  if (\"string\" == typeof e || \"number\" == typeof e) n += e;else if (\"object\" == typeof e) if (Array.isArray(e)) {\n    var o = e.length;\n    for (t = 0; t < o; t++) e[t] && (f = r(e[t])) && (n && (n += \" \"), n += f);\n  } else for (f in e) e[f] && (n && (n += \" \"), n += f);\n  return n;\n}\nexport function clsx() {\n  for (var e, t, f = 0, n = \"\", o = arguments.length; f < o; f++) (e = arguments[f]) && (t = r(e)) && (n && (n += \" \"), n += t);\n  return n;\n}\nexport default clsx;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}