{"database": {"host": "127.0.0.1", "port": 5432, "name": "auth_service_db", "user": "auth_service_user", "password": "auth_service_password", "connection_pool": {"min_connections": 5, "max_connections": 50, "connection_timeout": 30, "idle_timeout": 300}, "ssl": {"enabled": false, "mode": "require", "cert_file": "/etc/ssl/certs/postgresql.crt", "key_file": "/etc/ssl/private/postgresql.key"}}, "server": {"port": 8082, "host": "0.0.0.0", "log_level": "info", "worker_threads": 8, "max_request_size": 1048576, "request_timeout": 30, "keep_alive_timeout": 60, "cors": {"enabled": true, "allowed_origins": ["*"], "allowed_methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "allowed_headers": ["Content-Type", "Authorization", "X-Requested-With"], "allow_credentials": true, "max_age": 86400}, "ssl": {"enabled": false, "cert_file": "/etc/ssl/certs/auth-service.crt", "key_file": "/etc/ssl/private/auth-service.key", "protocols": ["TLSv1.2", "TLSv1.3"]}}, "oauth2": {"jwt": {"secret": "production-jwt-secret-key-change-this-in-production", "algorithm": "HS256", "issuer": "auth.chcit.org", "audience": "chcit.org", "access_token_expiry": 900, "refresh_token_expiry": 604800, "cleanup_interval": 3600, "max_tokens_per_user": 10}, "argon2": {"memory_cost": 65536, "time_cost": 3, "parallelism": 4, "salt_length": 32, "hash_length": 32}, "session": {"timeout": 86400, "cleanup_interval": 3600, "max_sessions_per_user": 5, "secure_cookies": true, "same_site": "strict"}, "rate_limiting": {"enabled": true, "login_attempts": {"max_attempts": 5, "window_minutes": 15, "lockout_minutes": 30}, "api_requests": {"max_requests": 1000, "window_minutes": 15}, "token_generation": {"max_tokens": 10, "window_minutes": 60}}}, "multi_tenant": {"enabled": true, "default_organization": "Default Organization", "default_project": "Default Project", "auto_create_projects": true, "max_organizations": 100, "max_projects_per_org": 50}, "rbac": {"enabled": true, "cache_permissions": true, "permission_cache_ttl": 300, "role_inheritance": true, "default_roles": ["user", "admin"], "max_roles_per_user": 10, "audit_permissions": true}, "security": {"password_policy": {"min_length": 8, "require_uppercase": true, "require_lowercase": true, "require_numbers": true, "require_special_chars": true, "max_age_days": 90, "history_count": 5}, "account_lockout": {"enabled": true, "max_failed_attempts": 5, "lockout_duration_minutes": 30, "reset_after_success": true}, "two_factor": {"enabled": false, "methods": ["totp", "sms"], "backup_codes": 10, "grace_period_hours": 24}}, "logging": {"level": "info", "format": "json", "output": "stdout", "file": {"enabled": false, "path": "/var/log/auth-service/auth-service.log", "max_size_mb": 100, "max_files": 10, "compress": true}, "audit": {"enabled": true, "events": ["login", "logout", "token_create", "token_revoke", "permission_check"], "retention_days": 90}}, "monitoring": {"metrics": {"enabled": true, "port": 9091, "path": "/metrics", "collection_interval": 60}, "health_check": {"enabled": true, "path": "/health", "database_check": true, "timeout_seconds": 5}, "performance": {"slow_query_threshold_ms": 1000, "memory_usage_alert_mb": 512, "cpu_usage_alert_percent": 80}}, "backup": {"enabled": false, "schedule": "0 2 * * *", "retention_days": 30, "compression": true, "encryption": false}, "development": {"debug_mode": false, "verbose_logging": false, "mock_external_services": false, "test_data": {"create_test_users": false, "test_organization": "Test Org", "test_project": "Test Project"}}}