# JWT Blacklist Troubleshooting Guide

**Version**: 1.0.0  
**Date**: July 17, 2025  
**Status**: ✅ **PRODUCTION READY**

## 🚨 **Common Issues and Solutions**

### **Issue 1: Tokens Not Being Added to Blacklist**

**Symptoms:**
- Token revocation returns success (`{}`)
- Token is marked as revoked in `auth_tokens` table
- No entries appear in `jwt_token_blacklist` table
- Logs show: `Token revoked (blacklist failed)`

**Root Cause:**
Database permission issues or logic errors in revocation flow.

**Solution:**
```sql
-- Check and fix database permissions
GRANT ALL PRIVILEGES ON TABLE jwt_token_blacklist TO auth_service_user;
GRANT ALL PRIVILEGES ON SEQUENCE jwt_token_blacklist_id_seq TO auth_service_user;

-- Verify permissions
\dp jwt_token_blacklist
```

**Verification:**
```bash
# Test token revocation
curl -X POST http://localhost:8082/oauth/revoke \
  -H "Content-Type: application/json" \
  -d '{"token": "YOUR_TOKEN_HERE"}'

# Check blacklist table
sudo -u postgres psql -d auth_service_db -c \
  "SELECT COUNT(*) FROM jwt_token_blacklist;"
```

---

### **Issue 2: Permission Denied Errors**

**Symptoms:**
- Error logs show: `ERROR: permission denied for table jwt_token_blacklist`
- Blacklist operations fail silently

**Root Cause:**
Missing database permissions for auth_service_user.

**Solution:**
```sql
-- Connect as postgres superuser
sudo -u postgres psql -d auth_service_db

-- Grant all necessary permissions
GRANT ALL PRIVILEGES ON TABLE jwt_token_blacklist TO auth_service_user;
GRANT ALL PRIVILEGES ON SEQUENCE jwt_token_blacklist_id_seq TO auth_service_user;
GRANT USAGE ON SCHEMA public TO auth_service_user;

-- Verify grants
SELECT grantee, privilege_type 
FROM information_schema.role_table_grants 
WHERE table_name = 'jwt_token_blacklist';
```

---

### **Issue 3: Blacklist Table Missing**

**Symptoms:**
- Error: `relation "jwt_token_blacklist" does not exist`
- Database schema appears incomplete

**Root Cause:**
Database migration not applied or schema file outdated.

**Solution:**
```sql
-- Create the blacklist table manually
CREATE TABLE IF NOT EXISTS jwt_token_blacklist (
    id SERIAL PRIMARY KEY,
    token_hash VARCHAR(64) UNIQUE NOT NULL,
    user_id UUID REFERENCES auth_users(user_id) ON DELETE CASCADE,
    revoked_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    reason VARCHAR(255),
    revoked_by UUID REFERENCES auth_users(user_id) ON DELETE SET NULL,
    
    CONSTRAINT jwt_blacklist_hash_length CHECK (LENGTH(token_hash) = 64),
    CONSTRAINT jwt_blacklist_expires_future CHECK (expires_at > revoked_at)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_jwt_blacklist_hash ON jwt_token_blacklist(token_hash);
CREATE INDEX IF NOT EXISTS idx_jwt_blacklist_expires ON jwt_token_blacklist(expires_at);

-- Grant permissions
GRANT ALL PRIVILEGES ON TABLE jwt_token_blacklist TO auth_service_user;
GRANT ALL PRIVILEGES ON SEQUENCE jwt_token_blacklist_id_seq TO auth_service_user;
```

---

### **Issue 4: Logic Error in Token Revocation**

**Symptoms:**
- Logs show: `Cannot revoke invalid token`
- Token validation fails before revocation

**Root Cause:**
Attempting to validate token after it's already been revoked.

**Solution:**
Ensure you're using the fixed version of `jwt_manager.cpp` where validation occurs BEFORE revocation:

```cpp
// CORRECT sequence (fixed version):
auto validation = validateToken(token_string);  // Validate FIRST
if (!validation.valid) return false;
bool revoked = database_manager_->revoke_jwt_token(token_hash);  // Then revoke
if (revoked) {
    database_manager_->blacklist_jwt_token(token_hash, validation.expires_at);
}
```

---

## 🔍 **Diagnostic Commands**

### **Check Blacklist Table Status**
```sql
-- Table exists and structure
\d jwt_token_blacklist

-- Current entries
SELECT 
    COUNT(*) as total_entries,
    COUNT(*) FILTER (WHERE expires_at > NOW()) as active_entries,
    COUNT(*) FILTER (WHERE expires_at <= NOW()) as expired_entries
FROM jwt_token_blacklist;

-- Recent entries
SELECT id, LEFT(token_hash, 16) as token_prefix, revoked_at, expires_at 
FROM jwt_token_blacklist 
ORDER BY revoked_at DESC 
LIMIT 5;
```

### **Check Database Permissions**
```sql
-- Check table permissions
SELECT grantee, privilege_type 
FROM information_schema.role_table_grants 
WHERE table_name = 'jwt_token_blacklist' 
AND grantee = 'auth_service_user';

-- Check sequence permissions
SELECT grantee, privilege_type 
FROM information_schema.role_usage_grants 
WHERE object_name = 'jwt_token_blacklist_id_seq' 
AND grantee = 'auth_service_user';
```

### **Check Service Logs**
```bash
# Check for blacklist-related messages
tail -f /opt/auth-service/logs/auth-service.log | grep -E "(blacklist|revok)"

# Check error logs
tail -f /opt/auth-service/logs/auth-service-error.log | grep -E "(blacklist|permission)"

# Check recent revocation attempts
grep "Token revoked" /opt/auth-service/logs/auth-service.log | tail -5
```

---

## 🧪 **Testing Procedures**

### **End-to-End Blacklist Test**
```bash
#!/bin/bash
echo "=== JWT Blacklist Functionality Test ==="

# 1. Get a token
TOKEN_RESPONSE=$(curl -s -X POST http://localhost:8082/oauth/token \
  -H "Content-Type: application/json" \
  -d '{"username": "testuser", "password": "testpass123", "grant_type": "password"}')

ACCESS_TOKEN=$(echo $TOKEN_RESPONSE | jq -r '.access_token')
echo "Token obtained: ${ACCESS_TOKEN:0:20}..."

# 2. Check blacklist before revocation
BEFORE_COUNT=$(sudo -u postgres psql -d auth_service_db -t -c \
  "SELECT COUNT(*) FROM jwt_token_blacklist;")
echo "Blacklist entries before: $BEFORE_COUNT"

# 3. Revoke token
REVOKE_RESPONSE=$(curl -s -X POST http://localhost:8082/oauth/revoke \
  -H "Content-Type: application/json" \
  -d "{\"token\": \"$ACCESS_TOKEN\"}")
echo "Revoke response: $REVOKE_RESPONSE"

# 4. Check blacklist after revocation
AFTER_COUNT=$(sudo -u postgres psql -d auth_service_db -t -c \
  "SELECT COUNT(*) FROM jwt_token_blacklist;")
echo "Blacklist entries after: $AFTER_COUNT"

# 5. Verify token is invalid
VALIDATE_RESPONSE=$(curl -s -X POST http://localhost:8082/oauth/validate \
  -H "Authorization: Bearer $ACCESS_TOKEN")
echo "Validation after revoke: $VALIDATE_RESPONSE"

# 6. Check logs
echo "Recent log entries:"
tail -3 /opt/auth-service/logs/auth-service.log | grep -E "(revok|blacklist)"
```

### **Performance Test**
```sql
-- Test blacklist lookup performance
EXPLAIN ANALYZE 
SELECT 1 FROM jwt_token_blacklist 
WHERE token_hash = 'test_hash_12345678901234567890123456789012345678901234567890123456' 
AND expires_at > NOW();

-- Check index usage
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE tablename = 'jwt_token_blacklist';
```

---

## 🔧 **Maintenance Tasks**

### **Cleanup Expired Entries**
```sql
-- Manual cleanup (run periodically)
DELETE FROM jwt_token_blacklist 
WHERE expires_at < NOW() - INTERVAL '1 day';

-- Check cleanup effectiveness
SELECT 
    COUNT(*) as total_entries,
    COUNT(*) FILTER (WHERE expires_at > NOW()) as active_entries,
    pg_size_pretty(pg_total_relation_size('jwt_token_blacklist')) as table_size
FROM jwt_token_blacklist;
```

### **Monitor Table Growth**
```sql
-- Table size monitoring
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
    pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
FROM pg_tables 
WHERE tablename = 'jwt_token_blacklist';

-- Row count trends
SELECT 
    DATE(revoked_at) as date,
    COUNT(*) as tokens_revoked
FROM jwt_token_blacklist 
WHERE revoked_at > NOW() - INTERVAL '7 days'
GROUP BY DATE(revoked_at)
ORDER BY date;
```

---

## 📊 **Monitoring Queries**

### **Health Check Query**
```sql
-- Quick health check
SELECT 
    'jwt_blacklist_health' as metric,
    CASE 
        WHEN COUNT(*) = 0 THEN 'OK - No blacklisted tokens'
        WHEN COUNT(*) < 1000 THEN 'OK - Normal blacklist size'
        WHEN COUNT(*) < 10000 THEN 'WARNING - Large blacklist'
        ELSE 'CRITICAL - Very large blacklist'
    END as status,
    COUNT(*) as total_entries,
    COUNT(*) FILTER (WHERE expires_at > NOW()) as active_entries
FROM jwt_token_blacklist;
```

### **Performance Metrics**
```sql
-- Index efficiency
SELECT 
    indexname,
    idx_scan as scans,
    idx_tup_read as tuples_read,
    idx_tup_fetch as tuples_fetched,
    ROUND(idx_tup_fetch::numeric / NULLIF(idx_tup_read, 0) * 100, 2) as efficiency_percent
FROM pg_stat_user_indexes 
WHERE tablename = 'jwt_token_blacklist';
```

---

## 🚨 **Emergency Procedures**

### **Complete Blacklist Reset**
```sql
-- ⚠️ CAUTION: This will remove ALL blacklisted tokens
-- Only use in emergency situations

-- Backup first
CREATE TABLE jwt_token_blacklist_backup AS 
SELECT * FROM jwt_token_blacklist;

-- Clear blacklist (tokens will still be revoked in auth_tokens)
TRUNCATE jwt_token_blacklist RESTART IDENTITY;

-- Verify
SELECT COUNT(*) FROM jwt_token_blacklist;
```

### **Restore from Backup**
```sql
-- Restore blacklist from backup
INSERT INTO jwt_token_blacklist 
SELECT * FROM jwt_token_blacklist_backup
ON CONFLICT (token_hash) DO NOTHING;

-- Verify restoration
SELECT COUNT(*) FROM jwt_token_blacklist;
```

---

**Last Updated**: July 17, 2025  
**Next Review**: August 2025
