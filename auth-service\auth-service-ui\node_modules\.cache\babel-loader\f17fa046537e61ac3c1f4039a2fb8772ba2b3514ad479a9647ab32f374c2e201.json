{"ast": null, "code": "export default function composeClasses(slots, getUtilityClass, classes = undefined) {\n  const output = {};\n  Object.keys(slots).forEach(\n  // `Object.keys(slots)` can't be wider than `T` because we infer `T` from `slots`.\n  // @ts-expect-error https://github.com/microsoft/TypeScript/pull/12253#issuecomment-263132208\n  slot => {\n    output[slot] = slots[slot].reduce((acc, key) => {\n      if (key) {\n        const utilityClass = getUtilityClass(key);\n        if (utilityClass !== '') {\n          acc.push(utilityClass);\n        }\n        if (classes && classes[key]) {\n          acc.push(classes[key]);\n        }\n      }\n      return acc;\n    }, []).join(' ');\n  });\n  return output;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}