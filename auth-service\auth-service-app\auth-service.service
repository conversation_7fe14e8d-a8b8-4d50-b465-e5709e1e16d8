[Unit]
Description=Auth Service OAuth 2.0 Authentication Server
After=network.target postgresql.service
Wants=postgresql.service

[Service]
Type=simple
User=btaylor-admin
Group=btaylor-admin
WorkingDirectory=/opt/auth-service
ExecStart=/opt/auth-service/bin/auth-service --config /opt/auth-service/config/auth-service.conf --port 8082
Restart=always
RestartSec=5
StandardOutput=append:/opt/auth-service/logs/auth-service.log
StandardError=append:/opt/auth-service/logs/auth-service-error.log

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/auth-service/logs

[Install]
WantedBy=multi-user.target
