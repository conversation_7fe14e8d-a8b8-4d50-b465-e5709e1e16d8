{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nvar _circle;\nconst _excluded = [\"active\", \"className\", \"completed\", \"error\", \"icon\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport CheckCircle from '../internal/svg-icons/CheckCircle';\nimport Warning from '../internal/svg-icons/Warning';\nimport SvgIcon from '../SvgIcon';\nimport stepIconClasses, { getStepIconUtilityClass } from './stepIconClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    active,\n    completed,\n    error\n  } = ownerState;\n  const slots = {\n    root: ['root', active && 'active', completed && 'completed', error && 'error'],\n    text: ['text']\n  };\n  return composeClasses(slots, getStepIconUtilityClass, classes);\n};\nconst StepIconRoot = styled(SvgIcon, {\n  name: 'MuiStepIcon',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(({\n  theme\n}) => ({\n  display: 'block',\n  transition: theme.transitions.create('color', {\n    duration: theme.transitions.duration.shortest\n  }),\n  color: (theme.vars || theme).palette.text.disabled,\n  [`&.${stepIconClasses.completed}`]: {\n    color: (theme.vars || theme).palette.primary.main\n  },\n  [`&.${stepIconClasses.active}`]: {\n    color: (theme.vars || theme).palette.primary.main\n  },\n  [`&.${stepIconClasses.error}`]: {\n    color: (theme.vars || theme).palette.error.main\n  }\n}));\nconst StepIconText = styled('text', {\n  name: 'MuiStepIcon',\n  slot: 'Text',\n  overridesResolver: (props, styles) => styles.text\n})(({\n  theme\n}) => ({\n  fill: (theme.vars || theme).palette.primary.contrastText,\n  fontSize: theme.typography.caption.fontSize,\n  fontFamily: theme.typography.fontFamily\n}));\nconst StepIcon = /*#__PURE__*/React.forwardRef(function StepIcon(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiStepIcon'\n  });\n  const {\n      active = false,\n      className: classNameProp,\n      completed = false,\n      error = false,\n      icon\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    active,\n    completed,\n    error\n  });\n  const classes = useUtilityClasses(ownerState);\n  if (typeof icon === 'number' || typeof icon === 'string') {\n    const className = clsx(classNameProp, classes.root);\n    if (error) {\n      return /*#__PURE__*/_jsx(StepIconRoot, _extends({\n        as: Warning,\n        className: className,\n        ref: ref,\n        ownerState: ownerState\n      }, other));\n    }\n    if (completed) {\n      return /*#__PURE__*/_jsx(StepIconRoot, _extends({\n        as: CheckCircle,\n        className: className,\n        ref: ref,\n        ownerState: ownerState\n      }, other));\n    }\n    return /*#__PURE__*/_jsxs(StepIconRoot, _extends({\n      className: className,\n      ref: ref,\n      ownerState: ownerState\n    }, other, {\n      children: [_circle || (_circle = /*#__PURE__*/_jsx(\"circle\", {\n        cx: \"12\",\n        cy: \"12\",\n        r: \"12\"\n      })), /*#__PURE__*/_jsx(StepIconText, {\n        className: classes.text,\n        x: \"12\",\n        y: \"12\",\n        textAnchor: \"middle\",\n        dominantBaseline: \"central\",\n        ownerState: ownerState,\n        children: icon\n      })]\n    }));\n  }\n  return icon;\n});\nprocess.env.NODE_ENV !== \"production\" ? StepIcon.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Whether this step is active.\n   * @default false\n   */\n  active: PropTypes.bool,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Mark the step as completed. Is passed to child components.\n   * @default false\n   */\n  completed: PropTypes.bool,\n  /**\n   * If `true`, the step is marked as failed.\n   * @default false\n   */\n  error: PropTypes.bool,\n  /**\n   * The label displayed in the step icon.\n   */\n  icon: PropTypes.node,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default StepIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}