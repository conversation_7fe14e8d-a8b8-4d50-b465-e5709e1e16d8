/**
 * @file test_command_line.cpp
 * @brief Test program for command line argument parsing
 * 
 * This is a standalone test program to verify the command line argument
 * parsing functionality without requiring the full auth-service dependencies.
 * 
 * Compile with:
 * g++ -std=c++23 -I./include -lboost_program_options test_command_line.cpp src/command_line_args.cpp -o test_command_line
 */

#include <iostream>
#include <vector>
#include <string>
#include "command_line_args.hpp"

using namespace AuthServiceCLI;

void testBasicArguments() {
    std::cout << "=== Testing Basic Arguments ===\n";
    
    CommandLineParser parser;
    
    // Test help
    std::vector<const char*> help_args = {"test_program", "--help"};
    try {
        auto args = parser.parse(help_args.size(), const_cast<char**>(help_args.data()));
        std::cout << "✅ Help flag parsed: " << (args.show_help ? "true" : "false") << "\n";
    } catch (const std::exception& e) {
        std::cout << "❌ Help test failed: " << e.what() << "\n";
    }
    
    // Test version
    std::vector<const char*> version_args = {"test_program", "--version"};
    try {
        auto args = parser.parse(version_args.size(), const_cast<char**>(version_args.data()));
        std::cout << "✅ Version flag parsed: " << (args.show_version ? "true" : "false") << "\n";
    } catch (const std::exception& e) {
        std::cout << "❌ Version test failed: " << e.what() << "\n";
    }
}

void testConfigurationArguments() {
    std::cout << "\n=== Testing Configuration Arguments ===\n";
    
    CommandLineParser parser;
    
    // Test custom config and port
    std::vector<const char*> config_args = {
        "test_program", 
        "--config", "/custom/path/auth-service.conf",
        "--port", "9090",
        "--log-level", "debug"
    };
    
    try {
        auto args = parser.parse(config_args.size(), const_cast<char**>(config_args.data()));
        std::cout << "✅ Config file: " << args.config_file << "\n";
        std::cout << "✅ Port: " << args.port << "\n";
        std::cout << "✅ Log level: " << CommandLineParser::logLevelToString(args.log_level) << "\n";
    } catch (const std::exception& e) {
        std::cout << "❌ Configuration test failed: " << e.what() << "\n";
    }
}

void testDatabaseOverrides() {
    std::cout << "\n=== Testing Database Override Arguments ===\n";
    
    CommandLineParser parser;
    
    std::vector<const char*> db_args = {
        "test_program",
        "--db-host", "custom-db-server.com",
        "--db-port", "5433",
        "--db-name", "custom_auth_db",
        "--db-user", "custom_user"
    };
    
    try {
        auto args = parser.parse(db_args.size(), const_cast<char**>(db_args.data()));
        std::cout << "✅ DB Host: " << (args.db_host ? *args.db_host : "not set") << "\n";
        std::cout << "✅ DB Port: " << (args.db_port ? std::to_string(*args.db_port) : "not set") << "\n";
        std::cout << "✅ DB Name: " << (args.db_name ? *args.db_name : "not set") << "\n";
        std::cout << "✅ DB User: " << (args.db_user ? *args.db_user : "not set") << "\n";
    } catch (const std::exception& e) {
        std::cout << "❌ Database override test failed: " << e.what() << "\n";
    }
}

void testAdvancedArguments() {
    std::cout << "\n=== Testing Advanced Arguments ===\n";
    
    CommandLineParser parser;
    
    std::vector<const char*> advanced_args = {
        "test_program",
        "--daemon",
        "--verbose",
        "--debug",
        "--max-connections", "100",
        "--worker-threads", "8",
        "--no-cors",
        "--metrics-port", "9091"
    };
    
    try {
        auto args = parser.parse(advanced_args.size(), const_cast<char**>(advanced_args.data()));
        std::cout << "✅ Daemon mode: " << (args.daemon_mode ? "true" : "false") << "\n";
        std::cout << "✅ Verbose: " << (args.verbose ? "true" : "false") << "\n";
        std::cout << "✅ Debug: " << (args.debug_mode ? "true" : "false") << "\n";
        std::cout << "✅ Max connections: " << (args.max_connections ? std::to_string(*args.max_connections) : "not set") << "\n";
        std::cout << "✅ Worker threads: " << (args.worker_threads ? std::to_string(*args.worker_threads) : "not set") << "\n";
        std::cout << "✅ CORS enabled: " << (args.enable_cors ? "true" : "false") << "\n";
        std::cout << "✅ Metrics port: " << (args.metrics_port ? std::to_string(*args.metrics_port) : "not set") << "\n";
    } catch (const std::exception& e) {
        std::cout << "❌ Advanced arguments test failed: " << e.what() << "\n";
    }
}

void testValidationErrors() {
    std::cout << "\n=== Testing Validation Errors ===\n";
    
    CommandLineParser parser;
    
    // Test invalid port
    std::vector<const char*> invalid_port_args = {"test_program", "--port", "99999"};
    try {
        auto args = parser.parse(invalid_port_args.size(), const_cast<char**>(invalid_port_args.data()));
        std::cout << "❌ Should have failed with invalid port\n";
    } catch (const ArgumentValidationException& e) {
        std::cout << "✅ Correctly caught validation error for invalid port\n";
        std::cout << "   Error details: " << e.what() << "\n";
    } catch (const std::exception& e) {
        std::cout << "❌ Unexpected error: " << e.what() << "\n";
    }
    
    // Test invalid log level
    std::vector<const char*> invalid_log_args = {"test_program", "--log-level", "invalid"};
    try {
        auto args = parser.parse(invalid_log_args.size(), const_cast<char**>(invalid_log_args.data()));
        std::cout << "❌ Should have failed with invalid log level\n";
    } catch (const CommandLineException& e) {
        std::cout << "✅ Correctly caught error for invalid log level\n";
        std::cout << "   Error details: " << e.what() << "\n";
    } catch (const std::exception& e) {
        std::cout << "❌ Unexpected error: " << e.what() << "\n";
    }
}

void testHelpAndVersionOutput() {
    std::cout << "\n=== Testing Help and Version Output ===\n";
    
    CommandLineParser parser;
    
    std::cout << "Help text:\n";
    std::cout << parser.getHelpText() << "\n";
    
    std::cout << "Version info:\n";
    std::cout << parser.getVersionInfo() << "\n";
}

int main() {
    std::cout << "Command Line Arguments Test Suite\n";
    std::cout << "==================================\n";
    
    try {
        testBasicArguments();
        testConfigurationArguments();
        testDatabaseOverrides();
        testAdvancedArguments();
        testValidationErrors();
        testHelpAndVersionOutput();
        
        std::cout << "\n=== Test Suite Complete ===\n";
        std::cout << "✅ All tests completed successfully!\n";
        
    } catch (const std::exception& e) {
        std::cout << "❌ Test suite failed: " << e.what() << "\n";
        return 1;
    }
    
    return 0;
}
