{"ast": null, "code": "export default function formControlState({\n  props,\n  states,\n  muiFormControl\n}) {\n  return states.reduce((acc, state) => {\n    acc[state] = props[state];\n    if (muiFormControl) {\n      if (typeof props[state] === 'undefined') {\n        acc[state] = muiFormControl[state];\n      }\n    }\n    return acc;\n  }, {});\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}