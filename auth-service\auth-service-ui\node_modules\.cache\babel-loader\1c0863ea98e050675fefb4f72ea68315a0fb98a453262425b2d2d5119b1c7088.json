{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"initialWidth\", \"width\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport getDisplayName from '@mui/utils/getDisplayName';\nimport { getThemeProps } from '@mui/system/useThemeProps';\nimport useTheme from '../styles/useTheme';\nimport useEnhancedEffect from '../utils/useEnhancedEffect';\nimport useMediaQuery from '../useMediaQuery';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst breakpointKeys = ['xs', 'sm', 'md', 'lg', 'xl'];\n\n// By default, returns true if screen width is the same or greater than the given breakpoint.\nexport const isWidthUp = (breakpoint, width, inclusive = true) => {\n  if (inclusive) {\n    return breakpointKeys.indexOf(breakpoint) <= breakpointKeys.indexOf(width);\n  }\n  return breakpointKeys.indexOf(breakpoint) < breakpointKeys.indexOf(width);\n};\n\n// By default, returns true if screen width is less than the given breakpoint.\nexport const isWidthDown = (breakpoint, width, inclusive = false) => {\n  if (inclusive) {\n    return breakpointKeys.indexOf(width) <= breakpointKeys.indexOf(breakpoint);\n  }\n  return breakpointKeys.indexOf(width) < breakpointKeys.indexOf(breakpoint);\n};\nconst withWidth = (options = {}) => Component => {\n  const {\n    withTheme: withThemeOption = false,\n    noSSR = false,\n    initialWidth: initialWidthOption\n  } = options;\n  function WithWidth(props) {\n    const contextTheme = useTheme();\n    const theme = props.theme || contextTheme;\n    const _getThemeProps = getThemeProps({\n        theme,\n        name: 'MuiWithWidth',\n        props\n      }),\n      {\n        initialWidth,\n        width\n      } = _getThemeProps,\n      other = _objectWithoutPropertiesLoose(_getThemeProps, _excluded);\n    const [mountedState, setMountedState] = React.useState(false);\n    useEnhancedEffect(() => {\n      setMountedState(true);\n    }, []);\n\n    /**\n     * innerWidth |xs      sm      md      lg      xl\n     *            |-------|-------|-------|-------|------>\n     * width      |  xs   |  sm   |  md   |  lg   |  xl\n     */\n    const keys = theme.breakpoints.keys.slice().reverse();\n    const widthComputed = keys.reduce((output, key) => {\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      const matches = useMediaQuery(theme.breakpoints.up(key));\n      return !output && matches ? key : output;\n    }, null);\n    const more = _extends({\n      width: width || (mountedState || noSSR ? widthComputed : undefined) || initialWidth || initialWidthOption\n    }, withThemeOption ? {\n      theme\n    } : {}, other);\n\n    // When rendering the component on the server,\n    // we have no idea about the client browser screen width.\n    // In order to prevent blinks and help the reconciliation of the React tree\n    // we are not rendering the child component.\n    //\n    // An alternative is to use the `initialWidth` property.\n    if (more.width === undefined) {\n      return null;\n    }\n    return /*#__PURE__*/_jsx(Component, _extends({}, more));\n  }\n  process.env.NODE_ENV !== \"production\" ? WithWidth.propTypes = {\n    /**\n     * As `window.innerWidth` is unavailable on the server,\n     * we default to rendering an empty component during the first mount.\n     * You might want to use a heuristic to approximate\n     * the screen width of the client browser screen width.\n     *\n     * For instance, you could be using the user-agent or the client-hints.\n     * https://caniuse.com/#search=client%20hint\n     */\n    initialWidth: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']),\n    /**\n     * @ignore\n     */\n    theme: PropTypes.object,\n    /**\n     * Bypass the width calculation logic.\n     */\n    width: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl'])\n  } : void 0;\n  if (process.env.NODE_ENV !== 'production') {\n    WithWidth.displayName = `WithWidth(${getDisplayName(Component)})`;\n  }\n  return WithWidth;\n};\nexport default withWidth;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}