{"ast": null, "code": "import CanceledError from \"../cancel/CanceledError.js\";\nimport AxiosError from \"../core/AxiosError.js\";\nimport utils from '../utils.js';\nconst composeSignals = (signals, timeout) => {\n  const {\n    length\n  } = signals = signals ? signals.filter(Boolean) : [];\n  if (timeout || length) {\n    let controller = new AbortController();\n    let aborted;\n    const onabort = function (reason) {\n      if (!aborted) {\n        aborted = true;\n        unsubscribe();\n        const err = reason instanceof Error ? reason : this.reason;\n        controller.abort(err instanceof AxiosError ? err : new CanceledError(err instanceof Error ? err.message : err));\n      }\n    };\n    let timer = timeout && setTimeout(() => {\n      timer = null;\n      onabort(new AxiosError(`timeout ${timeout} of ms exceeded`, AxiosError.ETIMEDOUT));\n    }, timeout);\n    const unsubscribe = () => {\n      if (signals) {\n        timer && clearTimeout(timer);\n        timer = null;\n        signals.forEach(signal => {\n          signal.unsubscribe ? signal.unsubscribe(onabort) : signal.removeEventListener('abort', onabort);\n        });\n        signals = null;\n      }\n    };\n    signals.forEach(signal => signal.addEventListener('abort', onabort));\n    const {\n      signal\n    } = controller;\n    signal.unsubscribe = () => utils.asap(unsubscribe);\n    return signal;\n  }\n};\nexport default composeSignals;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}