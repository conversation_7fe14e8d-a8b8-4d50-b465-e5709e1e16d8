-- Migration Script: Add JWT Token Blacklist Table
-- This script adds the missing jwt_token_blacklist table to existing auth-service databases
-- Run this script on databases that were created before this table was added to the schema

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ============================================================================
-- JWT Token Blacklist Table - For revoked tokens
-- ============================================================================
CREATE TABLE IF NOT EXISTS jwt_token_blacklist (
    id SERIAL PRIMARY KEY,
    token_hash VARCHAR(64) UNIQUE NOT NULL,
    user_id UUID REFERENCES auth_users(user_id) ON DELETE CASCADE,
    revoked_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    reason VARCHAR(255),
    revoked_by UUID REFERENCES auth_users(user_id) ON DELETE SET NULL,
    
    -- Constraints
    CONSTRAINT jwt_blacklist_hash_length CHECK (LENGTH(token_hash) = 64),
    CONSTRAINT jwt_blacklist_expires_future CHECK (expires_at > revoked_at)
);

-- ============================================================================
-- JWT Token Blacklist Indexes
-- ============================================================================
CREATE INDEX IF NOT EXISTS idx_jwt_blacklist_hash ON jwt_token_blacklist(token_hash);
CREATE INDEX IF NOT EXISTS idx_jwt_blacklist_user ON jwt_token_blacklist(user_id);
CREATE INDEX IF NOT EXISTS idx_jwt_blacklist_expires ON jwt_token_blacklist(expires_at);

-- ============================================================================
-- Verification
-- ============================================================================
\echo ''
\echo 'JWT Token Blacklist table migration completed!'
\echo ''

-- Show table structure
\d jwt_token_blacklist

\echo ''
\echo 'Migration completed successfully. The jwt_token_blacklist table is now available.'
\echo 'This will resolve the "relation jwt_token_blacklist does not exist" errors.'
\echo ''
