import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Alert,
  CircularProgress,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  AppBar,
  Toolbar,
  IconButton,
  Menu,
  MenuItem
} from '@mui/material';
import {
  Security as SecurityIcon,
  Token as TokenIcon,
  Refresh as RefreshIcon,
  Logout as LogoutIcon,
  MonitorHeart as HealthIcon,
  AccountCircle,
  MoreVert as MoreVertIcon
} from '@mui/icons-material';
import { AuthService } from '../services/authService';
import { HealthResponse, ValidationResponse } from '../types/auth';

interface DashboardProps {
  onLogout: () => void;
}

export const Dashboard: React.FC<DashboardProps> = ({ onLogout }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [healthData, setHealthData] = useState<HealthResponse | null>(null);
  const [tokenData, setTokenData] = useState<ValidationResponse | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const accessToken = AuthService.getAccessToken();
  const refreshToken = AuthService.getRefreshToken();

  useEffect(() => {
    checkHealth();
    validateCurrentToken();
  }, []);

  const checkHealth = async () => {
    try {
      const health = await AuthService.healthCheck();
      setHealthData(health);
    } catch (err: any) {
      console.warn('Health check failed:', err.message);
    }
  };

  const validateCurrentToken = async () => {
    if (!accessToken) return;

    try {
      const validation = await AuthService.validateToken({ token: accessToken });
      setTokenData(validation);
    } catch (err: any) {
      console.warn('Token validation failed:', err.message);
    }
  };

  const handleRefreshToken = async () => {
    if (!refreshToken) {
      setError('No refresh token available');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      await AuthService.refreshToken({ refresh_token: refreshToken, grant_type: 'refresh_token' });
      setSuccess('Token refreshed successfully');
      await validateCurrentToken();
    } catch (err: any) {
      setError(err.message || 'Token refresh failed');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    setLoading(true);
    try {
      await AuthService.logout();
      onLogout();
    } catch (err: any) {
      console.warn('Logout error:', err.message);
      onLogout(); // Logout anyway
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const formatTokenPreview = (token: string | null): string => {
    if (!token) return 'Not available';
    return `${token.substring(0, 20)}...${token.substring(token.length - 10)}`;
  };

  const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* App Bar */}
      <AppBar position="static">
        <Toolbar>
          <SecurityIcon sx={{ mr: 2 }} />
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            Auth Service Dashboard
          </Typography>
          <IconButton
            size="large"
            aria-label="account menu"
            aria-controls="menu-appbar"
            aria-haspopup="true"
            onClick={handleMenuOpen}
            color="inherit"
          >
            <AccountCircle />
          </IconButton>
          <Menu
            id="menu-appbar"
            anchorEl={anchorEl}
            anchorOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            keepMounted
            transformOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
          >
            <MenuItem onClick={handleLogout}>
              <LogoutIcon sx={{ mr: 1 }} />
              Logout
            </MenuItem>
          </Menu>
        </Toolbar>
      </AppBar>

      {/* Main Content */}
      <Box sx={{ p: 3 }}>
        {/* Alerts */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}
        {success && (
          <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
            {success}
          </Alert>
        )}

        <Grid container spacing={3}>
          {/* Service Health */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <HealthIcon sx={{ mr: 1, color: 'success.main' }} />
                  <Typography variant="h6">Service Health</Typography>
                </Box>
                {healthData ? (
                  <>
                    <Chip 
                      label={healthData.status} 
                      color="success" 
                      sx={{ mb: 2 }} 
                    />
                    <Typography variant="body2" color="text.secondary">
                      Service: {healthData.service}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Version: {healthData.version}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Last Check: {formatTimestamp(healthData.timestamp)}
                    </Typography>
                    <Divider sx={{ my: 2 }} />
                    <Typography variant="subtitle2" gutterBottom>
                      Available Endpoints:
                    </Typography>
                    <List dense>
                      {healthData.oauth2_endpoints.map((endpoint, index) => (
                        <ListItem key={index} sx={{ py: 0 }}>
                          <ListItemText 
                            primary={endpoint} 
                            primaryTypographyProps={{ variant: 'body2' }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </>
                ) : (
                  <Typography color="text.secondary">Loading health data...</Typography>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Token Information */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <TokenIcon sx={{ mr: 1, color: 'primary.main' }} />
                  <Typography variant="h6">Token Information</Typography>
                </Box>
                
                {tokenData ? (
                  <>
                    <Chip 
                      label={tokenData.valid ? 'Valid' : 'Invalid'} 
                      color={tokenData.valid ? 'success' : 'error'} 
                      sx={{ mb: 2 }} 
                    />
                    {tokenData.valid && (
                      <>
                        <Typography variant="body2" color="text.secondary">
                          User: {tokenData.username}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          User ID: {tokenData.user_id}
                        </Typography>
                        {tokenData.expires_at && (
                          <Typography variant="body2" color="text.secondary">
                            Expires: {formatTimestamp(tokenData.expires_at)}
                          </Typography>
                        )}
                        {tokenData.scopes && (
                          <Typography variant="body2" color="text.secondary">
                            Scopes: {tokenData.scopes.join(', ')}
                          </Typography>
                        )}
                      </>
                    )}
                  </>
                ) : (
                  <Typography color="text.secondary">Loading token data...</Typography>
                )}

                <Divider sx={{ my: 2 }} />
                
                <Typography variant="subtitle2" gutterBottom>
                  Access Token:
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ wordBreak: 'break-all', mb: 1 }}>
                  {formatTokenPreview(accessToken)}
                </Typography>
                
                <Typography variant="subtitle2" gutterBottom>
                  Refresh Token:
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ wordBreak: 'break-all' }}>
                  {formatTokenPreview(refreshToken)}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Actions */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Token Actions
                </Typography>
                <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                  <Button
                    variant="contained"
                    startIcon={loading ? <CircularProgress size={20} /> : <RefreshIcon />}
                    onClick={handleRefreshToken}
                    disabled={loading || !refreshToken}
                  >
                    Refresh Token
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<HealthIcon />}
                    onClick={checkHealth}
                    disabled={loading}
                  >
                    Check Health
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<TokenIcon />}
                    onClick={validateCurrentToken}
                    disabled={loading || !accessToken}
                  >
                    Validate Token
                  </Button>
                  <Button
                    variant="outlined"
                    color="error"
                    startIcon={<LogoutIcon />}
                    onClick={handleLogout}
                    disabled={loading}
                  >
                    Logout
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
};
