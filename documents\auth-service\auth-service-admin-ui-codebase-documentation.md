# Authentication Service Admin UI Codebase Documentation

**Update (July 2025):**
- Certificate sync is wildcard-only (`*.chcit.org`), no per-domain logic remains.
- PowerShell SSH execution is robust (Invoke-Expression is no longer used).
- All UI documentation is current as of July 2025.

*Last Updated: June 26, 2025*  
*Current Status: Phase 4 - Planned (Not Yet Implemented)*

## 📋 **Overview**

This document provides a comprehensive plan for the Authentication Service Admin UI, a React/TypeScript application for managing users, roles, and authentication settings.

**Base Path**: `D:\Coding_Projects\auth-service\auth-service-ui\`  
**Framework**: React 18 with TypeScript  
**Build Tool**: Vite  
**Target Platform**: Modern web browsers  
**Deployment**: Nginx static hosting  

---

## 🚨 **Current Status: NOT YET IMPLEMENTED**

### **Implementation Phase**: Phase 4 - Advanced Features & Production Hardening
- **Prerequisites**: Auth-service C++23 application must be fully functional (Phase 3 complete)
- **Planned Start**: After core authentication functionality is implemented
- **Estimated Timeline**: 2-3 weeks after Phase 3 completion

---

## 🏗️ **Planned Project Structure**

### **Root Directory Structure**
```
D:\Coding_Projects\auth-service\auth-service-ui\
├── public/                     # Static assets
│   ├── index.html             # Main HTML template
│   ├── favicon.ico            # Application icon
│   └── manifest.json          # PWA manifest
├── src/                       # Source code
│   ├── components/            # Reusable UI components
│   ├── pages/                 # Page components
│   ├── contexts/              # React contexts
│   ├── hooks/                 # Custom React hooks
│   ├── services/              # API services
│   ├── types/                 # TypeScript type definitions
│   ├── utils/                 # Utility functions
│   ├── styles/                # Global styles
│   ├── App.tsx                # Main application component
│   ├── main.tsx               # Application entry point
│   └── vite-env.d.ts          # Vite type definitions
├── package.json               # Dependencies and scripts
├── tsconfig.json              # TypeScript configuration
├── vite.config.ts             # Vite build configuration
├── tailwind.config.js         # Tailwind CSS configuration
└── README.md                  # Project documentation
```

---

## 📦 **Planned Dependencies**

### **Core Framework**
```json
{
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.8.0",
    "typescript": "^5.0.0"
  }
}
```

### **UI Framework & Styling**
```json
{
  "dependencies": {
    "@headlessui/react": "^1.7.0",
    "@heroicons/react": "^2.0.0",
    "tailwindcss": "^3.3.0",
    "clsx": "^1.2.0"
  }
}
```

### **State Management & API**
```json
{
  "dependencies": {
    "@tanstack/react-query": "^4.28.0",
    "axios": "^1.3.0",
    "zustand": "^4.3.0"
  }
}
```

### **Form Handling & Validation**
```json
{
  "dependencies": {
    "react-hook-form": "^7.43.0",
    "@hookform/resolvers": "^3.0.0",
    "zod": "^3.21.0"
  }
}
```

### **Authentication & Security**
```json
{
  "dependencies": {
    "js-cookie": "^3.0.0",
    "jwt-decode": "^3.1.0"
  }
}
```

### **Development Tools**
```json
{
  "devDependencies": {
    "@types/react": "^18.0.0",
    "@types/react-dom": "^18.0.0",
    "@vitejs/plugin-react": "^4.0.0",
    "vite": "^4.3.0",
    "eslint": "^8.38.0",
    "@typescript-eslint/eslint-plugin": "^5.57.0",
    "prettier": "^2.8.0"
  }
}
```

---

## 🎯 **Planned Core Components**

### **Authentication Components**
#### **LoginForm.tsx** ⏳ **PLANNED**
- **Purpose**: Admin login interface with MFA support
- **Features**: Username/password, TOTP, SMS verification
- **Dependencies**: react-hook-form, zod validation
- **API Integration**: POST /auth/login

#### **AuthGuard.tsx** ⏳ **PLANNED**
- **Purpose**: Route protection for admin areas
- **Features**: JWT token validation, role checking
- **Dependencies**: react-router-dom, jwt-decode
- **API Integration**: GET /auth/validate

### **User Management Components**
#### **UserList.tsx** ⏳ **PLANNED**
- **Purpose**: Display and manage all users
- **Features**: Search, filter, pagination, bulk actions
- **Dependencies**: @tanstack/react-query for data fetching
- **API Integration**: GET /users, DELETE /users/{id}

#### **UserForm.tsx** ⏳ **PLANNED**
- **Purpose**: Create and edit user accounts
- **Features**: Form validation, role assignment, password generation
- **Dependencies**: react-hook-form, zod validation
- **API Integration**: POST /users, PUT /users/{id}

#### **UserDetails.tsx** ⏳ **PLANNED**
- **Purpose**: View detailed user information and activity
- **Features**: Audit logs, session history, permissions
- **Dependencies**: @tanstack/react-query
- **API Integration**: GET /users/{id}, GET /users/{id}/audit

### **Role & Permission Components**
#### **RoleManager.tsx** ⏳ **PLANNED**
- **Purpose**: Manage roles and permissions
- **Features**: Create roles, assign permissions, role hierarchy
- **Dependencies**: react-hook-form for role editing
- **API Integration**: GET /roles, POST /roles, PUT /roles/{id}

#### **PermissionMatrix.tsx** ⏳ **PLANNED**
- **Purpose**: Visual permission assignment interface
- **Features**: Matrix view, bulk permission changes
- **Dependencies**: Custom grid component
- **API Integration**: GET /permissions, PUT /users/{id}/permissions

### **Security & Monitoring Components**
#### **SecurityDashboard.tsx** ⏳ **PLANNED**
- **Purpose**: Security overview and alerts
- **Features**: Failed login attempts, suspicious activity, system health
- **Dependencies**: Chart.js for visualizations
- **API Integration**: GET /security/dashboard, GET /audit/summary

#### **AuditLog.tsx** ⏳ **PLANNED**
- **Purpose**: Comprehensive audit trail viewer
- **Features**: Advanced filtering, export functionality
- **Dependencies**: Date picker, CSV export
- **API Integration**: GET /audit/logs

### **System Configuration Components**
#### **SystemSettings.tsx** ⏳ **PLANNED**
- **Purpose**: Configure authentication policies
- **Features**: Password policies, session timeouts, MFA settings
- **Dependencies**: react-hook-form for settings
- **API Integration**: GET /config, PUT /config

#### **IntegrationSettings.tsx** ⏳ **PLANNED**
- **Purpose**: Configure SSO and external integrations
- **Features**: OAuth providers, LDAP settings, API keys
- **Dependencies**: Form validation, secure input handling
- **API Integration**: GET /integrations, PUT /integrations/{type}

---

## 🔗 **Planned API Integration**

### **AuthService Class** ⏳ **PLANNED**
```typescript
class AuthService {
  private baseURL: string;
  private token: string | null;

  async login(credentials: LoginCredentials): Promise<AuthResponse>;
  async logout(): Promise<void>;
  async validateToken(): Promise<boolean>;
  async refreshToken(): Promise<string>;
  async setupMFA(method: MFAMethod): Promise<MFASetupResponse>;
}
```

### **UserService Class** ⏳ **PLANNED**
```typescript
class UserService {
  async getUsers(params: UserQueryParams): Promise<PaginatedUsers>;
  async createUser(userData: CreateUserRequest): Promise<User>;
  async updateUser(id: string, userData: UpdateUserRequest): Promise<User>;
  async deleteUser(id: string): Promise<void>;
  async getUserAuditLog(id: string): Promise<AuditEntry[]>;
}
```

### **RoleService Class** ⏳ **PLANNED**
```typescript
class RoleService {
  async getRoles(): Promise<Role[]>;
  async createRole(roleData: CreateRoleRequest): Promise<Role>;
  async updateRole(id: string, roleData: UpdateRoleRequest): Promise<Role>;
  async deleteRole(id: string): Promise<void>;
  async getPermissions(): Promise<Permission[]>;
}
```

---

## 🎨 **Planned UI/UX Design**

### **Design System**
- **Color Scheme**: Professional blue/gray palette for admin interface
- **Typography**: Inter font family for readability
- **Icons**: Heroicons for consistent iconography
- **Layout**: Responsive design with sidebar navigation

### **Key Pages**
1. **Dashboard**: Overview of users, recent activity, system health
2. **Users**: User management with advanced filtering and bulk operations
3. **Roles**: Role and permission management interface
4. **Security**: Security monitoring and audit logs
5. **Settings**: System configuration and integrations
6. **Profile**: Admin user profile and preferences

### **Responsive Design**
- **Desktop**: Full sidebar navigation with detailed views
- **Tablet**: Collapsible sidebar with optimized layouts
- **Mobile**: Bottom navigation with simplified interfaces

---

## 🔒 **Security Considerations**

### **Authentication Security**
- **JWT Token Storage**: Secure httpOnly cookies for token storage
- **CSRF Protection**: CSRF tokens for state-changing operations
- **Session Management**: Automatic token refresh and logout on expiry

### **Authorization**
- **Role-Based Access**: Different admin levels with restricted access
- **Route Protection**: All admin routes require authentication
- **API Security**: All API calls include authentication headers

### **Data Protection**
- **Input Validation**: Client-side and server-side validation
- **XSS Prevention**: Proper data sanitization and encoding
- **Audit Logging**: All admin actions logged for compliance

---

## 📊 **Implementation Phases**

### **Phase 4.1: Core Admin Interface** ⏳ **PLANNED**
- Basic authentication and user management
- Simple user CRUD operations
- Basic role assignment

### **Phase 4.2: Advanced Features** ⏳ **PLANNED**
- Multi-factor authentication setup
- Advanced user filtering and search
- Bulk user operations

### **Phase 4.3: Security & Monitoring** ⏳ **PLANNED**
- Security dashboard and alerts
- Comprehensive audit logging
- System health monitoring

### **Phase 4.4: Integrations** ⏳ **PLANNED**
- SSO configuration interface
- External system integrations
- API key management

---

## 🚀 **Deployment Configuration**

### **Build Configuration**
```typescript
// vite.config.ts
export default defineConfig({
  plugins: [react()],
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom'],
          ui: ['@headlessui/react', '@heroicons/react']
        }
      }
    }
  },
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8082',
        changeOrigin: true
      }
    }
  }
});
```

### **Nginx Configuration**
```nginx
server {
    listen 3001;
    server_name auth-admin.chcit.org;
    
    root /opt/auth-service-ui/dist;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    location /api/ {
        proxy_pass http://localhost:8082/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

---

## 📋 **Prerequisites for Implementation**

### **Required Before Starting**
1. **Auth-Service API**: Core authentication endpoints functional
2. **Database Schema**: User and role tables implemented
3. **JWT System**: Token generation and validation working
4. **API Documentation**: Complete endpoint specifications

### **Development Environment**
1. **Node.js**: Version 18+ for React development
2. **Package Manager**: npm or yarn for dependency management
3. **Development Server**: Vite dev server for hot reloading
4. **API Access**: Local auth-service running on port 8082

---

*This admin UI will be implemented in Phase 4 after the core authentication service is fully functional. The design prioritizes security, usability, and comprehensive user management capabilities.*
