#include "rbac_manager.hpp"
#include "database_manager.hpp"
#include <iostream>
#include <sstream>
#include <regex>
#include <random>

RBACManager::RBACManager(DatabaseManager* db_manager) 
    : db_manager_(db_manager) {
    if (!db_manager_) {
        throw std::invalid_argument("DatabaseManager cannot be null");
    }
}

RBACManager::~RBACManager() = default;

// ============================================================================
// Organization Management
// ============================================================================

std::string RBACManager::createOrganization(const std::string& org_name, 
                                           const std::string& org_domain) {
    try {
        if (org_name.empty()) {
            logError("createOrganization", "Organization name cannot be empty");
            return "";
        }

        // Check if organization name already exists
        auto existing = getOrganizationByName(org_name);
        if (existing.has_value()) {
            logError("createOrganization", "Organization name already exists: " + org_name);
            return "";
        }

        std::string org_id = db_manager_->generateUUID();
        if (org_id.empty()) {
            logError("createOrganization", "Failed to generate UUID");
            return "";
        }

        // SQL query to insert new organization
        std::string query = R"(
            INSERT INTO auth_organizations (org_id, org_name, org_domain, created_at, updated_at, is_active)
            VALUES ($1, $2, $3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, true)
            RETURNING org_id
        )";

        // Execute query through database manager
        std::vector<std::string> params = {org_id, org_name, org_domain};
        auto result = db_manager_->executeRBACQuery(query, params);

        if (result.has_value() && !result->empty()) {
            std::cout << "Organization created successfully: " << org_name << " with ID: " << org_id << std::endl;
            return org_id;
        } else {
            logError("createOrganization", "Database insertion failed");
            return "";
        }
        
    } catch (const std::exception& e) {
        logError("createOrganization", e.what());
        return "";
    }
}

std::optional<RBACManager::Organization> RBACManager::getOrganization(const std::string& org_id) {
    try {
        if (!isValidUUID(org_id)) {
            logError("getOrganization", "Invalid organization ID format");
            return std::nullopt;
        }

        // SQL query to get organization by ID
        std::string query = R"(
            SELECT org_id, org_name, org_domain, created_at, updated_at, is_active
            FROM auth_organizations
            WHERE org_id = $1 AND is_active = true
        )";

        std::vector<std::string> params = {org_id};
        auto result = db_manager_->executeRBACQuery(query, params);

        if (result.has_value() && !result->empty()) {
            const auto& row = (*result)[0];

            Organization org;
            org.org_id = row["org_id"].as<std::string>();
            org.org_name = row["org_name"].as<std::string>();
            org.org_domain = row["org_domain"].as<std::string>("");
            org.is_active = row["is_active"].as<bool>();

            // Parse timestamps (simplified for now)
            org.created_at = std::chrono::system_clock::now();
            org.updated_at = std::chrono::system_clock::now();

            return org;
        }

        return std::nullopt;
        
    } catch (const std::exception& e) {
        logError("getOrganization", e.what());
        return std::nullopt;
    }
}

std::optional<RBACManager::Organization> RBACManager::getOrganizationByName(const std::string& org_name) {
    try {
        if (org_name.empty()) {
            logError("getOrganizationByName", "Organization name cannot be empty");
            return std::nullopt;
        }

        // SQL query to get organization by name
        std::string query = R"(
            SELECT org_id, org_name, org_domain, created_at, updated_at, is_active
            FROM auth_organizations
            WHERE org_name = $1 AND is_active = true
        )";

        std::vector<std::string> params = {org_name};
        auto result = db_manager_->executeRBACQuery(query, params);

        if (result.has_value() && !result->empty()) {
            const auto& row = (*result)[0];

            Organization org;
            org.org_id = row["org_id"].as<std::string>();
            org.org_name = row["org_name"].as<std::string>();
            org.org_domain = row["org_domain"].as<std::string>("");
            org.is_active = row["is_active"].as<bool>();

            // Parse timestamps (simplified for now)
            org.created_at = std::chrono::system_clock::now();
            org.updated_at = std::chrono::system_clock::now();

            return org;
        }

        return std::nullopt;
        
    } catch (const std::exception& e) {
        logError("getOrganizationByName", e.what());
        return std::nullopt;
    }
}

bool RBACManager::updateOrganization(const std::string& org_id, 
                                    const std::string& org_name,
                                    const std::string& org_domain) {
    try {
        if (!isValidUUID(org_id) || org_name.empty()) {
            logError("updateOrganization", "Invalid parameters");
            return false;
        }

        // SQL query to update organization
        std::string query = R"(
            UPDATE auth_organizations
            SET org_name = $2, org_domain = $3, updated_at = CURRENT_TIMESTAMP
            WHERE org_id = $1 AND is_active = true
        )";

        std::vector<std::string> params = {org_id, org_name, org_domain};
        auto result = db_manager_->executeRBACQuery(query, params);

        if (result.has_value()) {
            std::cout << "Organization updated successfully: " << org_id << " to name: " << org_name << std::endl;
            return true;
        } else {
            logError("updateOrganization", "Database update failed");
            return false;
        }
        
    } catch (const std::exception& e) {
        logError("updateOrganization", e.what());
        return false;
    }
}

bool RBACManager::deleteOrganization(const std::string& org_id) {
    try {
        if (!isValidUUID(org_id)) {
            logError("deleteOrganization", "Invalid organization ID format");
            return false;
        }

        // Soft delete - set is_active to false
        std::string query = R"(
            UPDATE auth_organizations
            SET is_active = false, updated_at = CURRENT_TIMESTAMP
            WHERE org_id = $1
        )";

        std::vector<std::string> params = {org_id};
        auto result = db_manager_->executeRBACQuery(query, params);

        if (result.has_value()) {
            std::cout << "Organization soft deleted successfully: " << org_id << std::endl;
            return true;
        } else {
            logError("deleteOrganization", "Database update failed");
            return false;
        }
        
    } catch (const std::exception& e) {
        logError("deleteOrganization", e.what());
        return false;
    }
}

std::vector<RBACManager::Organization> RBACManager::listOrganizations(bool include_inactive) {
    try {
        std::string query = R"(
            SELECT org_id, org_name, org_domain, created_at, updated_at, is_active
            FROM auth_organizations
        )";
        
        if (!include_inactive) {
            query += " WHERE is_active = true";
        }
        
        query += " ORDER BY org_name";

        // TODO: Implement actual database query
        std::vector<Organization> organizations;
        return organizations;
        
    } catch (const std::exception& e) {
        logError("listOrganizations", e.what());
        return {};
    }
}

// ============================================================================
// Project Management
// ============================================================================

std::string RBACManager::createProject(const std::string& org_id,
                                      const std::string& project_name,
                                      const std::string& project_description) {
    try {
        if (!isValidUUID(org_id) || project_name.empty()) {
            logError("createProject", "Invalid parameters");
            return "";
        }

        // Verify organization exists
        auto org = getOrganization(org_id);
        if (!org.has_value()) {
            logError("createProject", "Organization not found: " + org_id);
            return "";
        }

        std::string project_id = db_manager_->generateUUID();
        if (project_id.empty()) {
            logError("createProject", "Failed to generate UUID");
            return "";
        }

        // SQL query to insert new project
        std::string query = R"(
            INSERT INTO auth_projects (project_id, org_id, project_name, project_description, created_at, updated_at, is_active)
            VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, true)
            RETURNING project_id
        )";

        std::vector<std::string> params = {project_id, org_id, project_name, project_description};
        auto result = db_manager_->executeRBACQuery(query, params);

        if (result.has_value() && !result->empty()) {
            std::cout << "Project created successfully: " << project_name << " in org: " << org_id << std::endl;
            return project_id;
        } else {
            logError("createProject", "Database insertion failed");
            return "";
        }
        
    } catch (const std::exception& e) {
        logError("createProject", e.what());
        return "";
    }
}

std::optional<RBACManager::Project> RBACManager::getProject(const std::string& project_id) {
    try {
        if (!isValidUUID(project_id)) {
            logError("getProject", "Invalid project ID format");
            return std::nullopt;
        }

        // SQL query to get project by ID
        std::string query = R"(
            SELECT project_id, org_id, project_name, project_description, created_at, updated_at, is_active
            FROM auth_projects
            WHERE project_id = $1 AND is_active = true
        )";

        std::vector<std::string> params = {project_id};
        auto result = db_manager_->executeRBACQuery(query, params);

        if (result.has_value() && !result->empty()) {
            const auto& row = (*result)[0];

            Project project;
            project.project_id = row["project_id"].as<std::string>();
            project.org_id = row["org_id"].as<std::string>();
            project.project_name = row["project_name"].as<std::string>();
            project.project_description = row["project_description"].as<std::string>("");
            project.is_active = row["is_active"].as<bool>();

            // Parse timestamps (simplified for now)
            project.created_at = std::chrono::system_clock::now();
            project.updated_at = std::chrono::system_clock::now();

            return project;
        }

        return std::nullopt;
        
    } catch (const std::exception& e) {
        logError("getProject", e.what());
        return std::nullopt;
    }
}

// ============================================================================
// Permission Validation
// ============================================================================

RBACManager::PermissionResult RBACManager::validatePermission(const std::string& user_id,
                                                             const std::string& project_id,
                                                             const std::string& permission_name) {
    PermissionResult result;
    result.has_permission = false;
    result.user_id = user_id;
    result.project_id = project_id;
    result.permission_name = permission_name;

    try {
        if (!isValidUUID(user_id) || !isValidUUID(project_id) || permission_name.empty()) {
            logError("validatePermission", "Invalid parameters");
            return result;
        }

        // Complex query to check user permissions through roles
        std::string query = R"(
            SELECT DISTINCT r.role_name
            FROM auth_user_project_roles upr
            JOIN auth_roles r ON upr.role_id = r.role_id
            JOIN auth_role_permissions rp ON r.role_id = rp.role_id
            JOIN auth_permissions p ON rp.permission_id = p.permission_id
            WHERE upr.user_id = $1
              AND upr.project_id = $2
              AND p.permission_name = $3
              AND upr.is_active = true
              AND (upr.expires_at IS NULL OR upr.expires_at > CURRENT_TIMESTAMP)
        )";

        std::vector<std::string> params = {user_id, project_id, permission_name};
        auto db_result = db_manager_->executeRBACQuery(query, params);

        if (db_result.has_value() && !db_result->empty()) {
            result.has_permission = true;

            // Collect roles that granted the permission
            for (const auto& row : *db_result) {
                result.roles.push_back(row["role_name"].as<std::string>());
            }
        }

        return result;
        
    } catch (const std::exception& e) {
        logError("validatePermission", e.what());
        return result;
    }
}

bool RBACManager::validateSystemPermission(const std::string& user_id,
                                         const std::string& permission_name) {
    try {
        if (!isValidUUID(user_id) || permission_name.empty()) {
            logError("validateSystemPermission", "Invalid parameters");
            return false;
        }

        // Check system-level permissions through system roles
        std::string query = R"(
            SELECT COUNT(*)
            FROM auth_user_project_roles upr
            JOIN auth_roles r ON upr.role_id = r.role_id
            JOIN auth_role_permissions rp ON r.role_id = rp.role_id
            JOIN auth_permissions p ON rp.permission_id = p.permission_id
            WHERE upr.user_id = $1
              AND p.permission_name = $2
              AND r.is_system_role = true
              AND upr.is_active = true
              AND (upr.expires_at IS NULL OR upr.expires_at > CURRENT_TIMESTAMP)
        )";

        std::vector<std::string> params = {user_id, permission_name};
        auto result = db_manager_->executeRBACQuery(query, params);

        if (result.has_value() && !result->empty()) {
            int count = (*result)[0][0].as<int>();
            return count > 0;
        }

        return false;
        
    } catch (const std::exception& e) {
        logError("validateSystemPermission", e.what());
        return false;
    }
}

// ============================================================================
// Helper Methods
// ============================================================================

bool RBACManager::isValidUUID(const std::string& uuid) {
    // Use database manager's UUID validation
    return db_manager_->isValidUUID(uuid);
}

std::string RBACManager::generateUUID() {
    // Use database manager's UUID generation
    return db_manager_->generateUUID();
}

void RBACManager::logError(const std::string& operation, const std::string& error) {
    std::cerr << "[RBACManager::" << operation << "] Error: " << error << std::endl;
}

// ============================================================================
// Missing Method Implementations
// ============================================================================

std::vector<RBACManager::Role> RBACManager::getUserProjectRoles(const std::string& user_id,
                                                               const std::string& project_id) {
    try {
        if (!isValidUUID(user_id) || !isValidUUID(project_id)) {
            logError("getUserProjectRoles", "Invalid user_id or project_id format");
            return {};
        }

        // SQL query to get user's roles in a specific project
        std::string query = R"(
            SELECT r.role_id, r.role_name, r.role_description, r.is_system_role, r.created_at
            FROM auth_user_project_roles upr
            JOIN auth_roles r ON upr.role_id = r.role_id
            WHERE upr.user_id = $1
              AND upr.project_id = $2
              AND upr.is_active = true
              AND (upr.expires_at IS NULL OR upr.expires_at > CURRENT_TIMESTAMP)
            ORDER BY r.role_name
        )";

        std::vector<std::string> params = {user_id, project_id};
        auto result = db_manager_->executeRBACQuery(query, params);

        std::vector<Role> roles;
        if (result.has_value() && !result->empty()) {
            for (const auto& row : *result) {
                Role role;
                role.role_id = row["role_id"].as<std::string>();
                role.role_name = row["role_name"].as<std::string>();
                role.role_description = row["role_description"].as<std::string>("");
                role.is_system_role = row["is_system_role"].as<bool>();

                // Parse timestamp (simplified for now)
                role.created_at = std::chrono::system_clock::now();

                roles.push_back(role);
            }
        }

        return roles;

    } catch (const std::exception& e) {
        logError("getUserProjectRoles", e.what());
        return {};
    }
}

std::vector<RBACManager::Permission> RBACManager::getRolePermissions(const std::string& role_id) {
    try {
        if (!isValidUUID(role_id)) {
            logError("getRolePermissions", "Invalid role_id format");
            return {};
        }

        // SQL query to get permissions for a specific role
        std::string query = R"(
            SELECT p.permission_id, p.permission_name, p.permission_description, p.created_at
            FROM auth_role_permissions rp
            JOIN auth_permissions p ON rp.permission_id = p.permission_id
            WHERE rp.role_id = $1
            ORDER BY p.permission_name
        )";

        std::vector<std::string> params = {role_id};
        auto result = db_manager_->executeRBACQuery(query, params);

        std::vector<Permission> permissions;
        if (result.has_value() && !result->empty()) {
            for (const auto& row : *result) {
                Permission permission;
                permission.permission_id = row["permission_id"].as<std::string>();
                permission.permission_name = row["permission_name"].as<std::string>();
                permission.permission_description = row["permission_description"].as<std::string>("");

                // Parse timestamp (simplified for now)
                permission.created_at = std::chrono::system_clock::now();

                permissions.push_back(permission);
            }
        }

        return permissions;

    } catch (const std::exception& e) {
        logError("getRolePermissions", e.what());
        return {};
    }
}

// ========================================================================
// Missing Method Implementations
// ========================================================================

std::vector<RBACManager::Project> RBACManager::listProjectsByOrganization(const std::string& org_id, bool include_inactive) {
    try {
        std::string query = R"(
            SELECT
                project_id,
                project_name,
                project_description,
                organization_id,
                is_active,
                created_at,
                updated_at
            FROM projects
            WHERE organization_id = $1
        )";

        if (!include_inactive) {
            query += " AND is_active = true";
        }

        query += " ORDER BY project_name";

        std::vector<std::string> params = {org_id};
        auto result = db_manager_->executeRBACQuery(query, params);

        std::vector<RBACManager::Project> projects;
        if (result.has_value() && !result->empty()) {
            for (const auto& row : *result) {
                RBACManager::Project project;
                project.project_id = row["project_id"].as<std::string>();
                project.project_name = row["project_name"].as<std::string>();
                project.project_description = row["project_description"].as<std::string>("");
                project.org_id = row["organization_id"].as<std::string>();
                project.is_active = row["is_active"].as<bool>();

                // Parse timestamps (simplified for now)
                project.created_at = std::chrono::system_clock::now();
                project.updated_at = std::chrono::system_clock::now();

                projects.push_back(project);
            }
        }

        return projects;

    } catch (const std::exception& e) {
        logError("listProjectsByOrganization", e.what());
        return {};
    }
}

std::vector<std::string> RBACManager::getUserProjectPermissions(const std::string& user_id, const std::string& project_id) {
    try {
        std::string query = R"(
            SELECT DISTINCT p.permission_name
            FROM user_project_roles upr
            JOIN role_permissions rp ON upr.role_id = rp.role_id
            JOIN permissions p ON rp.permission_id = p.permission_id
            WHERE upr.user_id = $1
              AND upr.project_id = $2
              AND upr.is_active = true
              AND (upr.expires_at IS NULL OR upr.expires_at > NOW())
            ORDER BY p.permission_name
        )";

        std::vector<std::string> params = {user_id, project_id};
        auto result = db_manager_->executeRBACQuery(query, params);

        std::vector<std::string> permissions;
        if (result.has_value() && !result->empty()) {
            for (const auto& row : *result) {
                permissions.push_back(row["permission_name"].as<std::string>());
            }
        }

        return permissions;

    } catch (const std::exception& e) {
        logError("getUserProjectPermissions", e.what());
        return {};
    }
}

bool RBACManager::assignUserProjectRole(const std::string& user_id, const std::string& project_id,
                                       const std::string& role_id,
                                       const std::optional<std::chrono::system_clock::time_point>& expires_at) {
    try {
        // Check if assignment already exists
        std::string checkQuery = R"(
            SELECT user_project_role_id
            FROM user_project_roles
            WHERE user_id = $1 AND project_id = $2 AND role_id = $3
        )";

        std::vector<std::string> checkParams = {user_id, project_id, role_id};
        auto existingResult = db_manager_->executeRBACQuery(checkQuery, checkParams);

        if (existingResult.has_value() && !existingResult->empty()) {
            // Update existing assignment
            std::string updateQuery = R"(
                UPDATE user_project_roles
                SET is_active = true,
                    expires_at = $4,
                    updated_at = NOW()
                WHERE user_id = $1 AND project_id = $2 AND role_id = $3
            )";

            std::vector<std::string> updateParams = {user_id, project_id, role_id};
            if (expires_at.has_value()) {
                // Convert time_point to string (simplified)
                updateParams.push_back("NULL"); // Would need proper timestamp conversion
            } else {
                updateParams.push_back("NULL");
            }

            auto updateResult = db_manager_->executeRBACQuery(updateQuery, updateParams);
            return updateResult.has_value();
        } else {
            // Create new assignment
            std::string insertQuery = R"(
                INSERT INTO user_project_roles
                (user_project_role_id, user_id, project_id, role_id, is_active, expires_at, created_at, updated_at)
                VALUES (gen_random_uuid(), $1, $2, $3, true, $4, NOW(), NOW())
            )";

            std::vector<std::string> insertParams = {user_id, project_id, role_id};
            if (expires_at.has_value()) {
                // Convert time_point to string (simplified)
                insertParams.push_back("NULL"); // Would need proper timestamp conversion
            } else {
                insertParams.push_back("NULL");
            }

            auto insertResult = db_manager_->executeRBACQuery(insertQuery, insertParams);
            return insertResult.has_value();
        }

    } catch (const std::exception& e) {
        logError("assignUserProjectRole", e.what());
        return false;
    }
}

bool RBACManager::removeUserProjectRole(const std::string& user_id, const std::string& project_id, const std::string& role_id) {
    try {
        std::string query = R"(
            UPDATE user_project_roles
            SET is_active = false, updated_at = NOW()
            WHERE user_id = $1 AND project_id = $2 AND role_id = $3
        )";

        std::vector<std::string> params = {user_id, project_id, role_id};
        auto result = db_manager_->executeRBACQuery(query, params);

        return result.has_value();

    } catch (const std::exception& e) {
        logError("removeUserProjectRole", e.what());
        return false;
    }
}
