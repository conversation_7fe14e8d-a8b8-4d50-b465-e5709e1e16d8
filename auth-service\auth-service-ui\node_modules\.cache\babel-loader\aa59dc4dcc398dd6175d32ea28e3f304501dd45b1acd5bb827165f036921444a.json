{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"TransitionComponent\", \"transitionDuration\", \"TransitionProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport Collapse from '../Collapse';\nimport StepperContext from '../Stepper/StepperContext';\nimport StepContext from '../Step/StepContext';\nimport { getStepContentUtilityClass } from './stepContentClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    last\n  } = ownerState;\n  const slots = {\n    root: ['root', last && 'last'],\n    transition: ['transition']\n  };\n  return composeClasses(slots, getStepContentUtilityClass, classes);\n};\nconst StepContentRoot = styled('div', {\n  name: 'MuiStepContent',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.last && styles.last];\n  }\n})(({\n  ownerState,\n  theme\n}) => _extends({\n  marginLeft: 12,\n  // half icon\n  paddingLeft: 8 + 12,\n  // margin + half icon\n  paddingRight: 8,\n  borderLeft: theme.vars ? `1px solid ${theme.vars.palette.StepContent.border}` : `1px solid ${theme.palette.mode === 'light' ? theme.palette.grey[400] : theme.palette.grey[600]}`\n}, ownerState.last && {\n  borderLeft: 'none'\n}));\nconst StepContentTransition = styled(Collapse, {\n  name: 'MuiStepContent',\n  slot: 'Transition',\n  overridesResolver: (props, styles) => styles.transition\n})({});\nconst StepContent = /*#__PURE__*/React.forwardRef(function StepContent(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiStepContent'\n  });\n  const {\n      children,\n      className,\n      TransitionComponent = Collapse,\n      transitionDuration: transitionDurationProp = 'auto',\n      TransitionProps\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    orientation\n  } = React.useContext(StepperContext);\n  const {\n    active,\n    last,\n    expanded\n  } = React.useContext(StepContext);\n  const ownerState = _extends({}, props, {\n    last\n  });\n  const classes = useUtilityClasses(ownerState);\n  if (process.env.NODE_ENV !== 'production') {\n    if (orientation !== 'vertical') {\n      console.error('MUI: <StepContent /> is only designed for use with the vertical stepper.');\n    }\n  }\n  let transitionDuration = transitionDurationProp;\n  if (transitionDurationProp === 'auto' && !TransitionComponent.muiSupportAuto) {\n    transitionDuration = undefined;\n  }\n  return /*#__PURE__*/_jsx(StepContentRoot, _extends({\n    className: clsx(classes.root, className),\n    ref: ref,\n    ownerState: ownerState\n  }, other, {\n    children: /*#__PURE__*/_jsx(StepContentTransition, _extends({\n      as: TransitionComponent,\n      in: active || expanded,\n      className: classes.transition,\n      ownerState: ownerState,\n      timeout: transitionDuration,\n      unmountOnExit: true\n    }, TransitionProps, {\n      children: children\n    }))\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? StepContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The component used for the transition.\n   * [Follow this guide](/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.\n   * @default Collapse\n   */\n  TransitionComponent: PropTypes.elementType,\n  /**\n   * Adjust the duration of the content expand transition.\n   * Passed as a prop to the transition component.\n   *\n   * Set to 'auto' to automatically calculate transition time based on height.\n   * @default 'auto'\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * Props applied to the transition element.\n   * By default, the element is based on this [`Transition`](https://reactcommunity.org/react-transition-group/transition/) component.\n   */\n  TransitionProps: PropTypes.object\n} : void 0;\nexport default StepContent;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}