# Auth Service - Configuration Guide

**Version**: 2.0.0  
**Updated**: 2025-07-17  
**File Format**: JSON  
**Default Location**: `/etc/auth-service/auth-service.conf`

## 📋 **Overview**

The auth-service application uses a comprehensive JSON-based configuration system that controls all aspects of the application, from database connections to security policies. This guide provides complete documentation for all available configuration options.

## 🗂️ **Configuration File Structure**

### **📁 File Locations**
- **Production**: `/etc/auth-service/auth-service.conf`
- **Development**: `config/auth-service.conf`
- **Custom**: Specified via `--config` command line argument

### **🔧 Configuration Sections**

## 1. **Database Configuration**

Controls PostgreSQL database connectivity and connection pooling.

```json
{
    "database": {
        "host": "127.0.0.1",
        "port": 5432,
        "name": "auth_service_db",
        "user": "auth_service_user",
        "password": "auth_service_password",
        "connection_pool": {
            "min_connections": 5,
            "max_connections": 50,
            "connection_timeout": 30,
            "idle_timeout": 300
        },
        "ssl": {
            "enabled": false,
            "mode": "require",
            "cert_file": "/etc/ssl/certs/postgresql.crt",
            "key_file": "/etc/ssl/private/postgresql.key"
        }
    }
}
```

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `host` | String | `127.0.0.1` | Database server hostname or IP |
| `port` | Integer | `5432` | Database server port |
| `name` | String | `auth_service_db` | Database name |
| `user` | String | `auth_service_user` | Database username |
| `password` | String | - | Database password |
| `connection_pool.min_connections` | Integer | `5` | Minimum connections in pool |
| `connection_pool.max_connections` | Integer | `50` | Maximum connections in pool |
| `connection_pool.connection_timeout` | Integer | `30` | Connection timeout (seconds) |
| `connection_pool.idle_timeout` | Integer | `300` | Idle connection timeout (seconds) |
| `ssl.enabled` | Boolean | `false` | Enable SSL for database connections |
| `ssl.mode` | String | `require` | SSL mode (disable, allow, prefer, require) |

## 2. **Server Configuration**

Controls HTTP server behavior, CORS, and SSL settings.

```json
{
    "server": {
        "port": 8082,
        "host": "0.0.0.0",
        "log_level": "info",
        "worker_threads": 8,
        "max_request_size": 1048576,
        "request_timeout": 30,
        "keep_alive_timeout": 60,
        "cors": {
            "enabled": true,
            "allowed_origins": ["*"],
            "allowed_methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            "allowed_headers": ["Content-Type", "Authorization"],
            "allow_credentials": true,
            "max_age": 86400
        },
        "ssl": {
            "enabled": false,
            "cert_file": "/etc/ssl/certs/auth-service.crt",
            "key_file": "/etc/ssl/private/auth-service.key",
            "protocols": ["TLSv1.2", "TLSv1.3"]
        }
    }
}
```

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `port` | Integer | `8082` | HTTP server port |
| `host` | String | `0.0.0.0` | Bind address (0.0.0.0 for all interfaces) |
| `log_level` | String | `info` | Logging level (debug, info, warn, error) |
| `worker_threads` | Integer | `8` | Number of worker threads |
| `max_request_size` | Integer | `1048576` | Maximum request size (bytes) |
| `request_timeout` | Integer | `30` | Request timeout (seconds) |
| `keep_alive_timeout` | Integer | `60` | Keep-alive timeout (seconds) |

## 3. **OAuth 2.0 Configuration**

### **JWT Token Settings**
```json
{
    "oauth2": {
        "jwt": {
            "secret": "production-jwt-secret-key-change-this-in-production",
            "algorithm": "HS256",
            "issuer": "auth.chcit.org",
            "audience": "chcit.org",
            "access_token_expiry": 900,
            "refresh_token_expiry": 604800,
            "cleanup_interval": 3600,
            "max_tokens_per_user": 10
        }
    }
}
```

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `secret` | String | - | JWT signing secret (CHANGE IN PRODUCTION!) |
| `algorithm` | String | `HS256` | JWT signing algorithm |
| `issuer` | String | `auth.chcit.org` | JWT issuer claim |
| `audience` | String | `chcit.org` | JWT audience claim |
| `access_token_expiry` | Integer | `900` | Access token lifetime (seconds) |
| `refresh_token_expiry` | Integer | `604800` | Refresh token lifetime (seconds) |
| `cleanup_interval` | Integer | `3600` | Token cleanup interval (seconds) |
| `max_tokens_per_user` | Integer | `10` | Maximum tokens per user |

### **Argon2 Password Hashing**
```json
{
    "oauth2": {
        "argon2": {
            "memory_cost": 65536,
            "time_cost": 3,
            "parallelism": 4,
            "salt_length": 32,
            "hash_length": 32
        }
    }
}
```

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `memory_cost` | Integer | `65536` | Memory usage (KB) |
| `time_cost` | Integer | `3` | Number of iterations |
| `parallelism` | Integer | `4` | Number of parallel threads |
| `salt_length` | Integer | `32` | Salt length (bytes) |
| `hash_length` | Integer | `32` | Hash output length (bytes) |

### **Session Management**
```json
{
    "oauth2": {
        "session": {
            "timeout": 86400,
            "cleanup_interval": 3600,
            "max_sessions_per_user": 5,
            "secure_cookies": true,
            "same_site": "strict"
        }
    }
}
```

### **Rate Limiting**
```json
{
    "oauth2": {
        "rate_limiting": {
            "enabled": true,
            "login_attempts": {
                "max_attempts": 5,
                "window_minutes": 15,
                "lockout_minutes": 30
            },
            "api_requests": {
                "max_requests": 1000,
                "window_minutes": 15
            },
            "token_generation": {
                "max_tokens": 10,
                "window_minutes": 60
            }
        }
    }
}
```

## 4. **Multi-tenant Configuration**

```json
{
    "multi_tenant": {
        "enabled": true,
        "default_organization": "Default Organization",
        "default_project": "Default Project",
        "auto_create_projects": true,
        "max_organizations": 100,
        "max_projects_per_org": 50
    }
}
```

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `enabled` | Boolean | `true` | Enable multi-tenant support |
| `default_organization` | String | `Default Organization` | Default organization name |
| `default_project` | String | `Default Project` | Default project name |
| `auto_create_projects` | Boolean | `true` | Auto-create projects for new users |
| `max_organizations` | Integer | `100` | Maximum number of organizations |
| `max_projects_per_org` | Integer | `50` | Maximum projects per organization |

## 5. **RBAC Configuration**

```json
{
    "rbac": {
        "enabled": true,
        "cache_permissions": true,
        "permission_cache_ttl": 300,
        "role_inheritance": true,
        "default_roles": ["user", "admin"],
        "max_roles_per_user": 10,
        "audit_permissions": true
    }
}
```

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `enabled` | Boolean | `true` | Enable RBAC system |
| `cache_permissions` | Boolean | `true` | Cache permission lookups |
| `permission_cache_ttl` | Integer | `300` | Permission cache TTL (seconds) |
| `role_inheritance` | Boolean | `true` | Enable role inheritance |
| `default_roles` | Array | `["user", "admin"]` | Default roles for new users |
| `max_roles_per_user` | Integer | `10` | Maximum roles per user |
| `audit_permissions` | Boolean | `true` | Audit permission checks |

## 📚 **Configuration Examples**

### **Development Configuration**
```json
{
    "database": {
        "host": "localhost",
        "port": 5432,
        "name": "auth_service_dev",
        "user": "dev_user",
        "password": "dev_password"
    },
    "server": {
        "port": 8082,
        "log_level": "debug",
        "cors": {
            "enabled": true,
            "allowed_origins": ["http://localhost:3000", "http://localhost:8080"]
        }
    },
    "oauth2": {
        "jwt": {
            "access_token_expiry": 3600,
            "secret": "development-secret-not-for-production"
        }
    },
    "development": {
        "debug_mode": true,
        "verbose_logging": true,
        "test_data": {
            "create_test_users": true
        }
    }
}
```

### **Production Configuration**
```json
{
    "database": {
        "host": "db.internal.company.com",
        "port": 5432,
        "name": "auth_service_prod",
        "user": "auth_service",
        "password": "${DB_PASSWORD}",
        "connection_pool": {
            "min_connections": 10,
            "max_connections": 100
        },
        "ssl": {
            "enabled": true,
            "mode": "require"
        }
    },
    "server": {
        "port": 8082,
        "host": "0.0.0.0",
        "log_level": "warn",
        "worker_threads": 16,
        "ssl": {
            "enabled": true,
            "cert_file": "/etc/ssl/certs/auth-service.crt",
            "key_file": "/etc/ssl/private/auth-service.key"
        }
    },
    "oauth2": {
        "jwt": {
            "secret": "${JWT_SECRET}",
            "access_token_expiry": 900,
            "issuer": "auth.company.com",
            "audience": "company.com"
        },
        "rate_limiting": {
            "enabled": true,
            "login_attempts": {
                "max_attempts": 3,
                "lockout_minutes": 60
            }
        }
    }
}
```

## 🔧 **Configuration Management**

### **Environment Variables**
You can use environment variable substitution in configuration values:
```json
{
    "database": {
        "password": "${DB_PASSWORD}",
        "host": "${DB_HOST:-localhost}"
    }
}
```

### **Configuration Validation**
Test your configuration file:
```bash
./auth-service --test-config --config /path/to/auth-service.conf
```

### **Configuration Override**
Command line arguments override configuration file settings:
```bash
./auth-service --config /path/to/config --port 9090 --db-host custom-db
```

## 🚨 **Security Considerations**

### **Critical Settings to Change in Production**
1. **JWT Secret**: Change `oauth2.jwt.secret` to a strong, unique value
2. **Database Password**: Use strong database credentials
3. **SSL**: Enable SSL for both database and server connections
4. **CORS**: Restrict `allowed_origins` to specific domains
5. **Rate Limiting**: Enable and tune rate limiting settings

### **File Permissions**
```bash
# Secure configuration file permissions
chmod 600 /etc/auth-service/auth-service.conf
chown auth-service:auth-service /etc/auth-service/auth-service.conf
```

## 📖 **Integration with Application**

The configuration is accessed through the `ConfigManager` class:

```cpp
#include "config_manager.hpp"

// Load configuration
ConfigManager config("/etc/auth-service/auth-service.conf");

// Access database settings
std::string db_host = config.get_database_host();
int db_port = config.get_database_port();

// Access OAuth 2.0 settings
std::string jwt_secret = config.get_jwt_secret();
int token_expiry = config.get_jwt_access_token_expiry();

// Access RBAC settings
bool rbac_enabled = config.get_rbac_enabled();
```

This comprehensive configuration system provides fine-grained control over all aspects of the auth-service application while maintaining security and operational flexibility.
