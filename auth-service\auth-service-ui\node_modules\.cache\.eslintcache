[{"D:\\Coding_Projects\\auth-service\\auth-service-ui\\src\\index.tsx": "1", "D:\\Coding_Projects\\auth-service\\auth-service-ui\\src\\App.tsx": "2", "D:\\Coding_Projects\\auth-service\\auth-service-ui\\src\\services\\authService.ts": "3", "D:\\Coding_Projects\\auth-service\\auth-service-ui\\src\\components\\LoginForm.tsx": "4", "D:\\Coding_Projects\\auth-service\\auth-service-ui\\src\\components\\Dashboard.tsx": "5"}, {"size": 274, "mtime": 1752335885734, "results": "6", "hashOfConfig": "7"}, {"size": 3243, "mtime": 1752839888756, "results": "8", "hashOfConfig": "7"}, {"size": 4691, "mtime": 1752335775573, "results": "9", "hashOfConfig": "7"}, {"size": 5663, "mtime": 1752842412062, "results": "10", "hashOfConfig": "7"}, {"size": 10889, "mtime": 1752842437788, "results": "11", "hashOfConfig": "7"}, {"filePath": "12", "messages": "13", "suppressedMessages": "14", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "13cw82n", {"filePath": "15", "messages": "16", "suppressedMessages": "17", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Coding_Projects\\auth-service\\auth-service-ui\\src\\index.tsx", [], [], "D:\\Coding_Projects\\auth-service\\auth-service-ui\\src\\App.tsx", [], [], "D:\\Coding_Projects\\auth-service\\auth-service-ui\\src\\services\\authService.ts", [], [], "D:\\Coding_Projects\\auth-service\\auth-service-ui\\src\\components\\LoginForm.tsx", [], [], "D:\\Coding_Projects\\auth-service\\auth-service-ui\\src\\components\\Dashboard.tsx", ["27", "28", "29"], [], {"ruleId": "30", "severity": 1, "message": "31", "line": 16, "column": 3, "nodeType": "32", "messageId": "33", "endLine": 16, "endColumn": 15}, {"ruleId": "30", "severity": 1, "message": "34", "line": 30, "column": 15, "nodeType": "32", "messageId": "33", "endLine": 30, "endColumn": 27}, {"ruleId": "35", "severity": 1, "message": "36", "line": 53, "column": 6, "nodeType": "37", "endLine": 53, "endColumn": 8, "suggestions": "38"}, "@typescript-eslint/no-unused-vars", "'ListItemIcon' is defined but never used.", "Identifier", "unusedVar", "'MoreVertIcon' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'validateCurrentToken'. Either include it or remove the dependency array.", "ArrayExpression", ["39"], {"desc": "40", "fix": "41"}, "Update the dependencies array to be: [validateCurrentToken]", {"range": "42", "text": "43"}, [1343, 1345], "[validateCurrentToken]"]