{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getToggleButtonGroupUtilityClass(slot) {\n  return generateUtilityClass('MuiToggleButtonGroup', slot);\n}\nconst toggleButtonGroupClasses = generateUtilityClasses('MuiToggleButtonGroup', ['root', 'selected', 'horizontal', 'vertical', 'disabled', 'grouped', 'groupedHorizontal', 'groupedVertical', 'fullWidth', 'firstButton', 'lastButton', 'middleButton']);\nexport default toggleButtonGroupClasses;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}