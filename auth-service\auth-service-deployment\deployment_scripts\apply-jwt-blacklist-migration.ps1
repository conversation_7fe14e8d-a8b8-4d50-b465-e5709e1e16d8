# Apply JWT Blacklist Table Migration
# This script applies the database migration to add the missing jwt_token_blacklist table

param(
    [string]$Environment = "auth-dev"
)

# Import required modules
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$modulesDir = Join-Path $scriptDir "modules"

Import-Module (Join-Path $modulesDir "Logging.psm1") -Force
Import-Module (Join-Path $modulesDir "Config-Management.psm1") -Force
Import-Module (Join-Path $modulesDir "SSH-Operations.psm1") -Force

# Initialize logging
Initialize-Logging

Write-Log -Message "=== JWT Blacklist Table Migration ===" -Level "UI" -ForegroundColor Cyan
Write-Log -Message "" -Level "UI"

try {
    # Load configuration
    $configPath = Join-Path $scriptDir "config\deployment-config.json"
    if (-not (Test-Path $configPath)) {
        Write-Log -Message "Configuration file not found: $configPath" -Level "UI" -ForegroundColor Red
        exit 1
    }

    $script:Config = Get-Content $configPath | ConvertFrom-Json
    
    # Set target environment
    Set-TargetEnvironment -Environment $Environment
    
    Write-Log -Message "Target Environment: $Environment" -Level "UI" -ForegroundColor Yellow
    Write-Log -Message "Target Server: $($script:Config.current_target.hostname)" -Level "UI" -ForegroundColor Yellow
    Write-Log -Message "" -Level "UI"
    
    # Test SSH connection
    Write-Log -Message "Testing SSH connection..." -Level "UI" -ForegroundColor Yellow
    if (-not (Test-SSHConnection)) {
        Write-Log -Message "SSH connection failed. Cannot proceed with migration." -Level "UI" -ForegroundColor Red
        exit 1
    }
    Write-Log -Message "✅ SSH connection successful" -Level "UI" -ForegroundColor Green
    
    # Copy migration script to server
    Write-Log -Message "Copying migration script to server..." -Level "UI" -ForegroundColor Yellow
    $migrationScript = Join-Path $scriptDir "..\deployment_files\schemas\add_jwt_blacklist_table.sql"
    
    if (-not (Test-Path $migrationScript)) {
        Write-Log -Message "Migration script not found: $migrationScript" -Level "UI" -ForegroundColor Red
        exit 1
    }
    
    $remoteScript = "/tmp/add_jwt_blacklist_table.sql"
    $copyResult = Copy-FileToRemote -LocalPath $migrationScript -RemotePath $remoteScript
    
    if (-not $copyResult) {
        Write-Log -Message "Failed to copy migration script to server" -Level "UI" -ForegroundColor Red
        exit 1
    }
    Write-Log -Message "✅ Migration script copied to server" -Level "UI" -ForegroundColor Green
    
    # Apply migration
    Write-Log -Message "Applying JWT blacklist table migration..." -Level "UI" -ForegroundColor Yellow
    
    $dbConfig = $script:Config.current_target.database
    $migrationCmd = "sudo -u postgres psql -d $($dbConfig.database) -f $remoteScript"
    
    Write-Log -Message "Executing migration command..." -Level "UI" -ForegroundColor Yellow
    $migrationResult = Invoke-RemoteCommand -Command $migrationCmd -Silent:$false
    
    if ($null -eq $migrationResult) {
        Write-Log -Message "Migration command failed" -Level "UI" -ForegroundColor Red
        exit 1
    }
    
    # Check if migration was successful
    if ($migrationResult -like "*Migration completed successfully*" -or $migrationResult -like "*CREATE TABLE*") {
        Write-Log -Message "✅ Migration applied successfully" -Level "UI" -ForegroundColor Green
    } else {
        Write-Log -Message "Migration may have failed. Please check the output above." -Level "UI" -ForegroundColor Yellow
    }
    
    # Verify table exists
    Write-Log -Message "Verifying jwt_token_blacklist table exists..." -Level "UI" -ForegroundColor Yellow
    $verifyCmd = "sudo -u postgres psql -d $($dbConfig.database) -c '\dt jwt_token_blacklist'"
    $verifyResult = Invoke-RemoteCommand -Command $verifyCmd -Silent:$false
    
    if ($verifyResult -like "*jwt_token_blacklist*") {
        Write-Log -Message "✅ jwt_token_blacklist table verified" -Level "UI" -ForegroundColor Green
    } else {
        Write-Log -Message "❌ jwt_token_blacklist table not found" -Level "UI" -ForegroundColor Red
        exit 1
    }
    
    # Clean up temporary file
    Invoke-RemoteCommand -Command "rm -f $remoteScript" -Silent:$true
    
    Write-Log -Message "" -Level "UI"
    Write-Log -Message "🎉 JWT Blacklist Migration Completed Successfully!" -Level "UI" -ForegroundColor Green
    Write-Log -Message "The jwt_token_blacklist table has been added to the database." -Level "UI" -ForegroundColor Green
    Write-Log -Message "This will resolve the 'relation jwt_token_blacklist does not exist' errors." -Level "UI" -ForegroundColor Green
    Write-Log -Message "" -Level "UI"
    
} catch {
    Write-Log -Message "Error during migration: $_" -Level "UI" -ForegroundColor Red
    exit 1
}

Write-Log -Message "Press Enter to continue..." -Level "UI" -ForegroundColor Yellow
Read-Host | Out-Null
