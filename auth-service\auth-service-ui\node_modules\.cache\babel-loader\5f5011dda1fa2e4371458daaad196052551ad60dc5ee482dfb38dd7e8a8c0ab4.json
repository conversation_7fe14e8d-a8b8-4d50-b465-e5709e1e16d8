{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"aria-label\", \"aria-valuetext\", \"aria-labelledby\", \"component\", \"components\", \"componentsProps\", \"color\", \"classes\", \"className\", \"disableSwap\", \"disabled\", \"getAriaLabel\", \"getAriaValueText\", \"marks\", \"max\", \"min\", \"name\", \"onChange\", \"onChangeCommitted\", \"orientation\", \"shiftStep\", \"size\", \"step\", \"scale\", \"slotProps\", \"slots\", \"tabIndex\", \"track\", \"value\", \"valueLabelDisplay\", \"valueLabelFormat\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha, lighten, darken } from '@mui/system/colorManipulator';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport isHostComponent from '@mui/utils/isHostComponent';\nimport { useSlider, valueToPercent } from './useSlider';\nimport { styled } from '../zero-styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport slotShouldForwardProp from '../styles/slotShouldForwardProp';\nimport shouldSpreadAdditionalProps from '../utils/shouldSpreadAdditionalProps';\nimport capitalize from '../utils/capitalize';\nimport BaseSliderValueLabel from './SliderValueLabel';\nimport sliderClasses, { getSliderUtilityClass } from './sliderClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction Identity(x) {\n  return x;\n}\nexport const SliderRoot = styled('span', {\n  name: 'MuiSlider',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[`color${capitalize(ownerState.color)}`], ownerState.size !== 'medium' && styles[`size${capitalize(ownerState.size)}`], ownerState.marked && styles.marked, ownerState.orientation === 'vertical' && styles.vertical, ownerState.track === 'inverted' && styles.trackInverted, ownerState.track === false && styles.trackFalse];\n  }\n})(({\n  theme\n}) => {\n  var _theme$vars;\n  return {\n    borderRadius: 12,\n    boxSizing: 'content-box',\n    display: 'inline-block',\n    position: 'relative',\n    cursor: 'pointer',\n    touchAction: 'none',\n    WebkitTapHighlightColor: 'transparent',\n    '@media print': {\n      colorAdjust: 'exact'\n    },\n    [`&.${sliderClasses.disabled}`]: {\n      pointerEvents: 'none',\n      cursor: 'default',\n      color: (theme.vars || theme).palette.grey[400]\n    },\n    [`&.${sliderClasses.dragging}`]: {\n      [`& .${sliderClasses.thumb}, & .${sliderClasses.track}`]: {\n        transition: 'none'\n      }\n    },\n    variants: [...Object.keys(((_theme$vars = theme.vars) != null ? _theme$vars : theme).palette).filter(key => {\n      var _theme$vars2;\n      return ((_theme$vars2 = theme.vars) != null ? _theme$vars2 : theme).palette[key].main;\n    }).map(color => ({\n      props: {\n        color\n      },\n      style: {\n        color: (theme.vars || theme).palette[color].main\n      }\n    })), {\n      props: {\n        orientation: 'horizontal'\n      },\n      style: {\n        height: 4,\n        width: '100%',\n        padding: '13px 0',\n        // The primary input mechanism of the device includes a pointing device of limited accuracy.\n        '@media (pointer: coarse)': {\n          // Reach 42px touch target, about ~8mm on screen.\n          padding: '20px 0'\n        }\n      }\n    }, {\n      props: {\n        orientation: 'horizontal',\n        size: 'small'\n      },\n      style: {\n        height: 2\n      }\n    }, {\n      props: {\n        orientation: 'horizontal',\n        marked: true\n      },\n      style: {\n        marginBottom: 20\n      }\n    }, {\n      props: {\n        orientation: 'vertical'\n      },\n      style: {\n        height: '100%',\n        width: 4,\n        padding: '0 13px',\n        // The primary input mechanism of the device includes a pointing device of limited accuracy.\n        '@media (pointer: coarse)': {\n          // Reach 42px touch target, about ~8mm on screen.\n          padding: '0 20px'\n        }\n      }\n    }, {\n      props: {\n        orientation: 'vertical',\n        size: 'small'\n      },\n      style: {\n        width: 2\n      }\n    }, {\n      props: {\n        orientation: 'vertical',\n        marked: true\n      },\n      style: {\n        marginRight: 44\n      }\n    }]\n  };\n});\nexport const SliderRail = styled('span', {\n  name: 'MuiSlider',\n  slot: 'Rail',\n  overridesResolver: (props, styles) => styles.rail\n})({\n  display: 'block',\n  position: 'absolute',\n  borderRadius: 'inherit',\n  backgroundColor: 'currentColor',\n  opacity: 0.38,\n  variants: [{\n    props: {\n      orientation: 'horizontal'\n    },\n    style: {\n      width: '100%',\n      height: 'inherit',\n      top: '50%',\n      transform: 'translateY(-50%)'\n    }\n  }, {\n    props: {\n      orientation: 'vertical'\n    },\n    style: {\n      height: '100%',\n      width: 'inherit',\n      left: '50%',\n      transform: 'translateX(-50%)'\n    }\n  }, {\n    props: {\n      track: 'inverted'\n    },\n    style: {\n      opacity: 1\n    }\n  }]\n});\nexport const SliderTrack = styled('span', {\n  name: 'MuiSlider',\n  slot: 'Track',\n  overridesResolver: (props, styles) => styles.track\n})(({\n  theme\n}) => {\n  var _theme$vars3;\n  return {\n    display: 'block',\n    position: 'absolute',\n    borderRadius: 'inherit',\n    border: '1px solid currentColor',\n    backgroundColor: 'currentColor',\n    transition: theme.transitions.create(['left', 'width', 'bottom', 'height'], {\n      duration: theme.transitions.duration.shortest\n    }),\n    variants: [{\n      props: {\n        size: 'small'\n      },\n      style: {\n        border: 'none'\n      }\n    }, {\n      props: {\n        orientation: 'horizontal'\n      },\n      style: {\n        height: 'inherit',\n        top: '50%',\n        transform: 'translateY(-50%)'\n      }\n    }, {\n      props: {\n        orientation: 'vertical'\n      },\n      style: {\n        width: 'inherit',\n        left: '50%',\n        transform: 'translateX(-50%)'\n      }\n    }, {\n      props: {\n        track: false\n      },\n      style: {\n        display: 'none'\n      }\n    }, ...Object.keys(((_theme$vars3 = theme.vars) != null ? _theme$vars3 : theme).palette).filter(key => {\n      var _theme$vars4;\n      return ((_theme$vars4 = theme.vars) != null ? _theme$vars4 : theme).palette[key].main;\n    }).map(color => ({\n      props: {\n        color,\n        track: 'inverted'\n      },\n      style: _extends({}, theme.vars ? {\n        backgroundColor: theme.vars.palette.Slider[`${color}Track`],\n        borderColor: theme.vars.palette.Slider[`${color}Track`]\n      } : _extends({\n        backgroundColor: lighten(theme.palette[color].main, 0.62),\n        borderColor: lighten(theme.palette[color].main, 0.62)\n      }, theme.applyStyles('dark', {\n        backgroundColor: darken(theme.palette[color].main, 0.5)\n      }), theme.applyStyles('dark', {\n        borderColor: darken(theme.palette[color].main, 0.5)\n      })))\n    }))]\n  };\n});\nexport const SliderThumb = styled('span', {\n  name: 'MuiSlider',\n  slot: 'Thumb',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.thumb, styles[`thumbColor${capitalize(ownerState.color)}`], ownerState.size !== 'medium' && styles[`thumbSize${capitalize(ownerState.size)}`]];\n  }\n})(({\n  theme\n}) => {\n  var _theme$vars5;\n  return {\n    position: 'absolute',\n    width: 20,\n    height: 20,\n    boxSizing: 'border-box',\n    borderRadius: '50%',\n    outline: 0,\n    backgroundColor: 'currentColor',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    transition: theme.transitions.create(['box-shadow', 'left', 'bottom'], {\n      duration: theme.transitions.duration.shortest\n    }),\n    '&::before': {\n      position: 'absolute',\n      content: '\"\"',\n      borderRadius: 'inherit',\n      width: '100%',\n      height: '100%',\n      boxShadow: (theme.vars || theme).shadows[2]\n    },\n    '&::after': {\n      position: 'absolute',\n      content: '\"\"',\n      borderRadius: '50%',\n      // 42px is the hit target\n      width: 42,\n      height: 42,\n      top: '50%',\n      left: '50%',\n      transform: 'translate(-50%, -50%)'\n    },\n    [`&.${sliderClasses.disabled}`]: {\n      '&:hover': {\n        boxShadow: 'none'\n      }\n    },\n    variants: [{\n      props: {\n        size: 'small'\n      },\n      style: {\n        width: 12,\n        height: 12,\n        '&::before': {\n          boxShadow: 'none'\n        }\n      }\n    }, {\n      props: {\n        orientation: 'horizontal'\n      },\n      style: {\n        top: '50%',\n        transform: 'translate(-50%, -50%)'\n      }\n    }, {\n      props: {\n        orientation: 'vertical'\n      },\n      style: {\n        left: '50%',\n        transform: 'translate(-50%, 50%)'\n      }\n    }, ...Object.keys(((_theme$vars5 = theme.vars) != null ? _theme$vars5 : theme).palette).filter(key => {\n      var _theme$vars6;\n      return ((_theme$vars6 = theme.vars) != null ? _theme$vars6 : theme).palette[key].main;\n    }).map(color => ({\n      props: {\n        color\n      },\n      style: {\n        [`&:hover, &.${sliderClasses.focusVisible}`]: _extends({}, theme.vars ? {\n          boxShadow: `0px 0px 0px 8px rgba(${theme.vars.palette[color].mainChannel} / 0.16)`\n        } : {\n          boxShadow: `0px 0px 0px 8px ${alpha(theme.palette[color].main, 0.16)}`\n        }, {\n          '@media (hover: none)': {\n            boxShadow: 'none'\n          }\n        }),\n        [`&.${sliderClasses.active}`]: _extends({}, theme.vars ? {\n          boxShadow: `0px 0px 0px 14px rgba(${theme.vars.palette[color].mainChannel} / 0.16)`\n        } : {\n          boxShadow: `0px 0px 0px 14px ${alpha(theme.palette[color].main, 0.16)}`\n        })\n      }\n    }))]\n  };\n});\nexport const SliderValueLabel = styled(BaseSliderValueLabel, {\n  name: 'MuiSlider',\n  slot: 'ValueLabel',\n  overridesResolver: (props, styles) => styles.valueLabel\n})(({\n  theme\n}) => _extends({\n  zIndex: 1,\n  whiteSpace: 'nowrap'\n}, theme.typography.body2, {\n  fontWeight: 500,\n  transition: theme.transitions.create(['transform'], {\n    duration: theme.transitions.duration.shortest\n  }),\n  position: 'absolute',\n  backgroundColor: (theme.vars || theme).palette.grey[600],\n  borderRadius: 2,\n  color: (theme.vars || theme).palette.common.white,\n  display: 'flex',\n  alignItems: 'center',\n  justifyContent: 'center',\n  padding: '0.25rem 0.75rem',\n  variants: [{\n    props: {\n      orientation: 'horizontal'\n    },\n    style: {\n      transform: 'translateY(-100%) scale(0)',\n      top: '-10px',\n      transformOrigin: 'bottom center',\n      '&::before': {\n        position: 'absolute',\n        content: '\"\"',\n        width: 8,\n        height: 8,\n        transform: 'translate(-50%, 50%) rotate(45deg)',\n        backgroundColor: 'inherit',\n        bottom: 0,\n        left: '50%'\n      },\n      [`&.${sliderClasses.valueLabelOpen}`]: {\n        transform: 'translateY(-100%) scale(1)'\n      }\n    }\n  }, {\n    props: {\n      orientation: 'vertical'\n    },\n    style: {\n      transform: 'translateY(-50%) scale(0)',\n      right: '30px',\n      top: '50%',\n      transformOrigin: 'right center',\n      '&::before': {\n        position: 'absolute',\n        content: '\"\"',\n        width: 8,\n        height: 8,\n        transform: 'translate(-50%, -50%) rotate(45deg)',\n        backgroundColor: 'inherit',\n        right: -8,\n        top: '50%'\n      },\n      [`&.${sliderClasses.valueLabelOpen}`]: {\n        transform: 'translateY(-50%) scale(1)'\n      }\n    }\n  }, {\n    props: {\n      size: 'small'\n    },\n    style: {\n      fontSize: theme.typography.pxToRem(12),\n      padding: '0.25rem 0.5rem'\n    }\n  }, {\n    props: {\n      orientation: 'vertical',\n      size: 'small'\n    },\n    style: {\n      right: '20px'\n    }\n  }]\n}));\nexport const SliderMark = styled('span', {\n  name: 'MuiSlider',\n  slot: 'Mark',\n  shouldForwardProp: prop => slotShouldForwardProp(prop) && prop !== 'markActive',\n  overridesResolver: (props, styles) => {\n    const {\n      markActive\n    } = props;\n    return [styles.mark, markActive && styles.markActive];\n  }\n})(({\n  theme\n}) => ({\n  position: 'absolute',\n  width: 2,\n  height: 2,\n  borderRadius: 1,\n  backgroundColor: 'currentColor',\n  variants: [{\n    props: {\n      orientation: 'horizontal'\n    },\n    style: {\n      top: '50%',\n      transform: 'translate(-1px, -50%)'\n    }\n  }, {\n    props: {\n      orientation: 'vertical'\n    },\n    style: {\n      left: '50%',\n      transform: 'translate(-50%, 1px)'\n    }\n  }, {\n    props: {\n      markActive: true\n    },\n    style: {\n      backgroundColor: (theme.vars || theme).palette.background.paper,\n      opacity: 0.8\n    }\n  }]\n}));\nexport const SliderMarkLabel = styled('span', {\n  name: 'MuiSlider',\n  slot: 'MarkLabel',\n  shouldForwardProp: prop => slotShouldForwardProp(prop) && prop !== 'markLabelActive',\n  overridesResolver: (props, styles) => styles.markLabel\n})(({\n  theme\n}) => _extends({}, theme.typography.body2, {\n  color: (theme.vars || theme).palette.text.secondary,\n  position: 'absolute',\n  whiteSpace: 'nowrap',\n  variants: [{\n    props: {\n      orientation: 'horizontal'\n    },\n    style: {\n      top: 30,\n      transform: 'translateX(-50%)',\n      '@media (pointer: coarse)': {\n        top: 40\n      }\n    }\n  }, {\n    props: {\n      orientation: 'vertical'\n    },\n    style: {\n      left: 36,\n      transform: 'translateY(50%)',\n      '@media (pointer: coarse)': {\n        left: 44\n      }\n    }\n  }, {\n    props: {\n      markLabelActive: true\n    },\n    style: {\n      color: (theme.vars || theme).palette.text.primary\n    }\n  }]\n}));\nconst useUtilityClasses = ownerState => {\n  const {\n    disabled,\n    dragging,\n    marked,\n    orientation,\n    track,\n    classes,\n    color,\n    size\n  } = ownerState;\n  const slots = {\n    root: ['root', disabled && 'disabled', dragging && 'dragging', marked && 'marked', orientation === 'vertical' && 'vertical', track === 'inverted' && 'trackInverted', track === false && 'trackFalse', color && `color${capitalize(color)}`, size && `size${capitalize(size)}`],\n    rail: ['rail'],\n    track: ['track'],\n    mark: ['mark'],\n    markActive: ['markActive'],\n    markLabel: ['markLabel'],\n    markLabelActive: ['markLabelActive'],\n    valueLabel: ['valueLabel'],\n    thumb: ['thumb', disabled && 'disabled', size && `thumbSize${capitalize(size)}`, color && `thumbColor${capitalize(color)}`],\n    active: ['active'],\n    disabled: ['disabled'],\n    focusVisible: ['focusVisible']\n  };\n  return composeClasses(slots, getSliderUtilityClass, classes);\n};\nconst Forward = ({\n  children\n}) => children;\nconst Slider = /*#__PURE__*/React.forwardRef(function Slider(inputProps, ref) {\n  var _ref, _slots$root, _ref2, _slots$rail, _ref3, _slots$track, _ref4, _slots$thumb, _ref5, _slots$valueLabel, _ref6, _slots$mark, _ref7, _slots$markLabel, _ref8, _slots$input, _slotProps$root, _slotProps$rail, _slotProps$track, _slotProps$thumb, _slotProps$valueLabel, _slotProps$mark, _slotProps$markLabel, _slotProps$input;\n  const props = useDefaultProps({\n    props: inputProps,\n    name: 'MuiSlider'\n  });\n  const isRtl = useRtl();\n  const {\n      'aria-label': ariaLabel,\n      'aria-valuetext': ariaValuetext,\n      'aria-labelledby': ariaLabelledby,\n      // eslint-disable-next-line react/prop-types\n      component = 'span',\n      components = {},\n      componentsProps = {},\n      color = 'primary',\n      classes: classesProp,\n      className,\n      disableSwap = false,\n      disabled = false,\n      getAriaLabel,\n      getAriaValueText,\n      marks: marksProp = false,\n      max = 100,\n      min = 0,\n      orientation = 'horizontal',\n      shiftStep = 10,\n      size = 'medium',\n      step = 1,\n      scale = Identity,\n      slotProps,\n      slots,\n      track = 'normal',\n      valueLabelDisplay = 'off',\n      valueLabelFormat = Identity\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    isRtl,\n    max,\n    min,\n    classes: classesProp,\n    disabled,\n    disableSwap,\n    orientation,\n    marks: marksProp,\n    color,\n    size,\n    step,\n    shiftStep,\n    scale,\n    track,\n    valueLabelDisplay,\n    valueLabelFormat\n  });\n  const {\n    axisProps,\n    getRootProps,\n    getHiddenInputProps,\n    getThumbProps,\n    open,\n    active,\n    axis,\n    focusedThumbIndex,\n    range,\n    dragging,\n    marks,\n    values,\n    trackOffset,\n    trackLeap,\n    getThumbStyle\n  } = useSlider(_extends({}, ownerState, {\n    rootRef: ref\n  }));\n  ownerState.marked = marks.length > 0 && marks.some(mark => mark.label);\n  ownerState.dragging = dragging;\n  ownerState.focusedThumbIndex = focusedThumbIndex;\n  const classes = useUtilityClasses(ownerState);\n\n  // support both `slots` and `components` for backward compatibility\n  const RootSlot = (_ref = (_slots$root = slots == null ? void 0 : slots.root) != null ? _slots$root : components.Root) != null ? _ref : SliderRoot;\n  const RailSlot = (_ref2 = (_slots$rail = slots == null ? void 0 : slots.rail) != null ? _slots$rail : components.Rail) != null ? _ref2 : SliderRail;\n  const TrackSlot = (_ref3 = (_slots$track = slots == null ? void 0 : slots.track) != null ? _slots$track : components.Track) != null ? _ref3 : SliderTrack;\n  const ThumbSlot = (_ref4 = (_slots$thumb = slots == null ? void 0 : slots.thumb) != null ? _slots$thumb : components.Thumb) != null ? _ref4 : SliderThumb;\n  const ValueLabelSlot = (_ref5 = (_slots$valueLabel = slots == null ? void 0 : slots.valueLabel) != null ? _slots$valueLabel : components.ValueLabel) != null ? _ref5 : SliderValueLabel;\n  const MarkSlot = (_ref6 = (_slots$mark = slots == null ? void 0 : slots.mark) != null ? _slots$mark : components.Mark) != null ? _ref6 : SliderMark;\n  const MarkLabelSlot = (_ref7 = (_slots$markLabel = slots == null ? void 0 : slots.markLabel) != null ? _slots$markLabel : components.MarkLabel) != null ? _ref7 : SliderMarkLabel;\n  const InputSlot = (_ref8 = (_slots$input = slots == null ? void 0 : slots.input) != null ? _slots$input : components.Input) != null ? _ref8 : 'input';\n  const rootSlotProps = (_slotProps$root = slotProps == null ? void 0 : slotProps.root) != null ? _slotProps$root : componentsProps.root;\n  const railSlotProps = (_slotProps$rail = slotProps == null ? void 0 : slotProps.rail) != null ? _slotProps$rail : componentsProps.rail;\n  const trackSlotProps = (_slotProps$track = slotProps == null ? void 0 : slotProps.track) != null ? _slotProps$track : componentsProps.track;\n  const thumbSlotProps = (_slotProps$thumb = slotProps == null ? void 0 : slotProps.thumb) != null ? _slotProps$thumb : componentsProps.thumb;\n  const valueLabelSlotProps = (_slotProps$valueLabel = slotProps == null ? void 0 : slotProps.valueLabel) != null ? _slotProps$valueLabel : componentsProps.valueLabel;\n  const markSlotProps = (_slotProps$mark = slotProps == null ? void 0 : slotProps.mark) != null ? _slotProps$mark : componentsProps.mark;\n  const markLabelSlotProps = (_slotProps$markLabel = slotProps == null ? void 0 : slotProps.markLabel) != null ? _slotProps$markLabel : componentsProps.markLabel;\n  const inputSlotProps = (_slotProps$input = slotProps == null ? void 0 : slotProps.input) != null ? _slotProps$input : componentsProps.input;\n  const rootProps = useSlotProps({\n    elementType: RootSlot,\n    getSlotProps: getRootProps,\n    externalSlotProps: rootSlotProps,\n    externalForwardedProps: other,\n    additionalProps: _extends({}, shouldSpreadAdditionalProps(RootSlot) && {\n      as: component\n    }),\n    ownerState: _extends({}, ownerState, rootSlotProps == null ? void 0 : rootSlotProps.ownerState),\n    className: [classes.root, className]\n  });\n  const railProps = useSlotProps({\n    elementType: RailSlot,\n    externalSlotProps: railSlotProps,\n    ownerState,\n    className: classes.rail\n  });\n  const trackProps = useSlotProps({\n    elementType: TrackSlot,\n    externalSlotProps: trackSlotProps,\n    additionalProps: {\n      style: _extends({}, axisProps[axis].offset(trackOffset), axisProps[axis].leap(trackLeap))\n    },\n    ownerState: _extends({}, ownerState, trackSlotProps == null ? void 0 : trackSlotProps.ownerState),\n    className: classes.track\n  });\n  const thumbProps = useSlotProps({\n    elementType: ThumbSlot,\n    getSlotProps: getThumbProps,\n    externalSlotProps: thumbSlotProps,\n    ownerState: _extends({}, ownerState, thumbSlotProps == null ? void 0 : thumbSlotProps.ownerState),\n    className: classes.thumb\n  });\n  const valueLabelProps = useSlotProps({\n    elementType: ValueLabelSlot,\n    externalSlotProps: valueLabelSlotProps,\n    ownerState: _extends({}, ownerState, valueLabelSlotProps == null ? void 0 : valueLabelSlotProps.ownerState),\n    className: classes.valueLabel\n  });\n  const markProps = useSlotProps({\n    elementType: MarkSlot,\n    externalSlotProps: markSlotProps,\n    ownerState,\n    className: classes.mark\n  });\n  const markLabelProps = useSlotProps({\n    elementType: MarkLabelSlot,\n    externalSlotProps: markLabelSlotProps,\n    ownerState,\n    className: classes.markLabel\n  });\n  const inputSliderProps = useSlotProps({\n    elementType: InputSlot,\n    getSlotProps: getHiddenInputProps,\n    externalSlotProps: inputSlotProps,\n    ownerState\n  });\n  return /*#__PURE__*/_jsxs(RootSlot, _extends({}, rootProps, {\n    children: [/*#__PURE__*/_jsx(RailSlot, _extends({}, railProps)), /*#__PURE__*/_jsx(TrackSlot, _extends({}, trackProps)), marks.filter(mark => mark.value >= min && mark.value <= max).map((mark, index) => {\n      const percent = valueToPercent(mark.value, min, max);\n      const style = axisProps[axis].offset(percent);\n      let markActive;\n      if (track === false) {\n        markActive = values.indexOf(mark.value) !== -1;\n      } else {\n        markActive = track === 'normal' && (range ? mark.value >= values[0] && mark.value <= values[values.length - 1] : mark.value <= values[0]) || track === 'inverted' && (range ? mark.value <= values[0] || mark.value >= values[values.length - 1] : mark.value >= values[0]);\n      }\n      return /*#__PURE__*/_jsxs(React.Fragment, {\n        children: [/*#__PURE__*/_jsx(MarkSlot, _extends({\n          \"data-index\": index\n        }, markProps, !isHostComponent(MarkSlot) && {\n          markActive\n        }, {\n          style: _extends({}, style, markProps.style),\n          className: clsx(markProps.className, markActive && classes.markActive)\n        })), mark.label != null ? /*#__PURE__*/_jsx(MarkLabelSlot, _extends({\n          \"aria-hidden\": true,\n          \"data-index\": index\n        }, markLabelProps, !isHostComponent(MarkLabelSlot) && {\n          markLabelActive: markActive\n        }, {\n          style: _extends({}, style, markLabelProps.style),\n          className: clsx(classes.markLabel, markLabelProps.className, markActive && classes.markLabelActive),\n          children: mark.label\n        })) : null]\n      }, index);\n    }), values.map((value, index) => {\n      const percent = valueToPercent(value, min, max);\n      const style = axisProps[axis].offset(percent);\n      const ValueLabelComponent = valueLabelDisplay === 'off' ? Forward : ValueLabelSlot;\n      return /*#__PURE__*/ /* TODO v6: Change component structure. It will help in avoiding the complicated React.cloneElement API added in SliderValueLabel component. Should be: Thumb -> Input, ValueLabel. Follow Joy UI's Slider structure. */_jsx(ValueLabelComponent, _extends({}, !isHostComponent(ValueLabelComponent) && {\n        valueLabelFormat,\n        valueLabelDisplay,\n        value: typeof valueLabelFormat === 'function' ? valueLabelFormat(scale(value), index) : valueLabelFormat,\n        index,\n        open: open === index || active === index || valueLabelDisplay === 'on',\n        disabled\n      }, valueLabelProps, {\n        children: /*#__PURE__*/_jsx(ThumbSlot, _extends({\n          \"data-index\": index\n        }, thumbProps, {\n          className: clsx(classes.thumb, thumbProps.className, active === index && classes.active, focusedThumbIndex === index && classes.focusVisible),\n          style: _extends({}, style, getThumbStyle(index), thumbProps.style),\n          children: /*#__PURE__*/_jsx(InputSlot, _extends({\n            \"data-index\": index,\n            \"aria-label\": getAriaLabel ? getAriaLabel(index) : ariaLabel,\n            \"aria-valuenow\": scale(value),\n            \"aria-labelledby\": ariaLabelledby,\n            \"aria-valuetext\": getAriaValueText ? getAriaValueText(scale(value), index) : ariaValuetext,\n            value: values[index]\n          }, inputSliderProps))\n        }))\n      }), index);\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Slider.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The label of the slider.\n   */\n  'aria-label': chainPropTypes(PropTypes.string, props => {\n    const range = Array.isArray(props.value || props.defaultValue);\n    if (range && props['aria-label'] != null) {\n      return new Error('MUI: You need to use the `getAriaLabel` prop instead of `aria-label` when using a range slider.');\n    }\n    return null;\n  }),\n  /**\n   * The id of the element containing a label for the slider.\n   */\n  'aria-labelledby': PropTypes.string,\n  /**\n   * A string value that provides a user-friendly name for the current value of the slider.\n   */\n  'aria-valuetext': chainPropTypes(PropTypes.string, props => {\n    const range = Array.isArray(props.value || props.defaultValue);\n    if (range && props['aria-valuetext'] != null) {\n      return new Error('MUI: You need to use the `getAriaValueText` prop instead of `aria-valuetext` when using a range slider.');\n    }\n    return null;\n  }),\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'primary'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The components used for each slot inside.\n   *\n   * @deprecated use the `slots` prop instead. This prop will be removed in v7. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/).\n   *\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Input: PropTypes.elementType,\n    Mark: PropTypes.elementType,\n    MarkLabel: PropTypes.elementType,\n    Rail: PropTypes.elementType,\n    Root: PropTypes.elementType,\n    Thumb: PropTypes.elementType,\n    Track: PropTypes.elementType,\n    ValueLabel: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * @deprecated use the `slotProps` prop instead. This prop will be removed in v7. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/).\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    input: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    mark: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    markLabel: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    rail: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    thumb: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    track: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    valueLabel: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n      children: PropTypes.element,\n      className: PropTypes.string,\n      open: PropTypes.bool,\n      style: PropTypes.object,\n      value: PropTypes.number,\n      valueLabelDisplay: PropTypes.oneOf(['auto', 'off', 'on'])\n    })])\n  }),\n  /**\n   * The default value. Use when the component is not controlled.\n   */\n  defaultValue: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.number), PropTypes.number]),\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the active thumb doesn't swap when moving pointer over a thumb while dragging another thumb.\n   * @default false\n   */\n  disableSwap: PropTypes.bool,\n  /**\n   * Accepts a function which returns a string value that provides a user-friendly name for the thumb labels of the slider.\n   * This is important for screen reader users.\n   * @param {number} index The thumb label's index to format.\n   * @returns {string}\n   */\n  getAriaLabel: PropTypes.func,\n  /**\n   * Accepts a function which returns a string value that provides a user-friendly name for the current value of the slider.\n   * This is important for screen reader users.\n   * @param {number} value The thumb label's value to format.\n   * @param {number} index The thumb label's index to format.\n   * @returns {string}\n   */\n  getAriaValueText: PropTypes.func,\n  /**\n   * Marks indicate predetermined values to which the user can move the slider.\n   * If `true` the marks are spaced according the value of the `step` prop.\n   * If an array, it should contain objects with `value` and an optional `label` keys.\n   * @default false\n   */\n  marks: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.shape({\n    label: PropTypes.node,\n    value: PropTypes.number.isRequired\n  })), PropTypes.bool]),\n  /**\n   * The maximum allowed value of the slider.\n   * Should not be equal to min.\n   * @default 100\n   */\n  max: PropTypes.number,\n  /**\n   * The minimum allowed value of the slider.\n   * Should not be equal to max.\n   * @default 0\n   */\n  min: PropTypes.number,\n  /**\n   * Name attribute of the hidden `input` element.\n   */\n  name: PropTypes.string,\n  /**\n   * Callback function that is fired when the slider's value changed.\n   *\n   * @param {Event} event The event source of the callback.\n   * You can pull out the new value by accessing `event.target.value` (any).\n   * **Warning**: This is a generic event not a change event.\n   * @param {number | number[]} value The new value.\n   * @param {number} activeThumb Index of the currently moved thumb.\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback function that is fired when the `mouseup` is triggered.\n   *\n   * @param {React.SyntheticEvent | Event} event The event source of the callback. **Warning**: This is a generic event not a change event.\n   * @param {number | number[]} value The new value.\n   */\n  onChangeCommitted: PropTypes.func,\n  /**\n   * The component orientation.\n   * @default 'horizontal'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * A transformation function, to change the scale of the slider.\n   * @param {any} x\n   * @returns {any}\n   * @default function Identity(x) {\n   *   return x;\n   * }\n   */\n  scale: PropTypes.func,\n  /**\n   * The granularity with which the slider can step through values when using Page Up/Page Down or Shift + Arrow Up/Arrow Down.\n   * @default 10\n   */\n  shiftStep: PropTypes.number,\n  /**\n   * The size of the slider.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['small', 'medium']), PropTypes.string]),\n  /**\n   * The props used for each slot inside the Slider.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    input: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    mark: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    markLabel: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    rail: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    thumb: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    track: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    valueLabel: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n      children: PropTypes.element,\n      className: PropTypes.string,\n      open: PropTypes.bool,\n      style: PropTypes.object,\n      value: PropTypes.number,\n      valueLabelDisplay: PropTypes.oneOf(['auto', 'off', 'on'])\n    })])\n  }),\n  /**\n   * The components used for each slot inside the Slider.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    input: PropTypes.elementType,\n    mark: PropTypes.elementType,\n    markLabel: PropTypes.elementType,\n    rail: PropTypes.elementType,\n    root: PropTypes.elementType,\n    thumb: PropTypes.elementType,\n    track: PropTypes.elementType,\n    valueLabel: PropTypes.elementType\n  }),\n  /**\n   * The granularity with which the slider can step through values. (A \"discrete\" slider.)\n   * The `min` prop serves as the origin for the valid values.\n   * We recommend (max - min) to be evenly divisible by the step.\n   *\n   * When step is `null`, the thumb can only be slid onto marks provided with the `marks` prop.\n   * @default 1\n   */\n  step: PropTypes.number,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Tab index attribute of the hidden `input` element.\n   */\n  tabIndex: PropTypes.number,\n  /**\n   * The track presentation:\n   *\n   * - `normal` the track will render a bar representing the slider value.\n   * - `inverted` the track will render a bar representing the remaining slider value.\n   * - `false` the track will render without a bar.\n   * @default 'normal'\n   */\n  track: PropTypes.oneOf(['inverted', 'normal', false]),\n  /**\n   * The value of the slider.\n   * For ranged sliders, provide an array with two values.\n   */\n  value: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.number), PropTypes.number]),\n  /**\n   * Controls when the value label is displayed:\n   *\n   * - `auto` the value label will display when the thumb is hovered or focused.\n   * - `on` will display persistently.\n   * - `off` will never display.\n   * @default 'off'\n   */\n  valueLabelDisplay: PropTypes.oneOf(['auto', 'off', 'on']),\n  /**\n   * The format function the value label's value.\n   *\n   * When a function is provided, it should have the following signature:\n   *\n   * - {number} value The value label's value to format\n   * - {number} index The value label's index to format\n   * @param {any} x\n   * @returns {any}\n   * @default function Identity(x) {\n   *   return x;\n   * }\n   */\n  valueLabelFormat: PropTypes.oneOfType([PropTypes.func, PropTypes.string])\n} : void 0;\nexport default Slider;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}