{"ast": null, "code": "const defaultGenerator = componentName => componentName;\nconst createClassNameGenerator = () => {\n  let generate = defaultGenerator;\n  return {\n    configure(generator) {\n      generate = generator;\n    },\n    generate(componentName) {\n      return generate(componentName);\n    },\n    reset() {\n      generate = defaultGenerator;\n    }\n  };\n};\nconst ClassNameGenerator = createClassNameGenerator();\nexport default ClassNameGenerator;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}